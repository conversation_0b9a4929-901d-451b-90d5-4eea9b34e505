from django.db import models
from authentication.models import TcdUserConsult


class TcdSetting(models.Model):
    name_th = models.CharField(max_length=255, db_collation='Thai_CI_AI', blank=True, null=True)
    name_en = models.CharField(max_length=255, db_collation='Thai_CI_AI', blank=True, null=True)
    addr_th = models.Char<PERSON>ield(max_length=255, db_collation='Thai_CI_AI', blank=True, null=True)
    addr_en = models.CharField(max_length=255, db_collation='Thai_CI_AI', blank=True, null=True)
    tel = models.CharField(max_length=1000, db_collation='Thai_CI_AI', blank=True, null=True)
    fax = models.CharField(max_length=255, db_collation='Thai_CI_AI', blank=True, null=True)
    email = models.Char<PERSON>ield(max_length=255, db_collation='Thai_CI_AI', blank=True, null=True)
    time_th = models.CharField(max_length=255, db_collation='Thai_CI_AI', blank=True, null=True)
    time_en = models.Char<PERSON>ield(max_length=255, db_collation='Thai_CI_AI', blank=True, null=True)
    url = models.CharField(max_length=255, db_collation='Thai_CI_AI', blank=True, null=True)
    title = models.CharField(max_length=255, db_collation='Thai_CI_AI', blank=True, null=True)
    description = models.CharField(max_length=255, db_collation='Thai_CI_AI', blank=True, null=True)
    keywords = models.CharField(max_length=255, db_collation='Thai_CI_AI', blank=True, null=True)
    host = models.CharField(max_length=255, db_collation='Thai_CI_AI', blank=True, null=True)
    port = models.CharField(max_length=255, db_collation='Thai_CI_AI', blank=True, null=True)
    username = models.CharField(max_length=255, db_collation='Thai_CI_AI', blank=True, null=True)
    password = models.CharField(max_length=255, db_collation='Thai_CI_AI', blank=True, null=True)
    fb = models.CharField(max_length=255, db_collation='Thai_CI_AI', blank=True, null=True)
    yt = models.CharField(max_length=255, db_collation='Thai_CI_AI', blank=True, null=True)
    pay_independent = models.IntegerField(blank=True, null=True)
    pay_independent_renew = models.IntegerField(blank=True, null=True)
    pay_independent_charge = models.IntegerField(blank=True, null=True)
    pay_juristic = models.IntegerField(blank=True, null=True)
    pay_juristic_renew = models.IntegerField(blank=True, null=True)
    pay_juristic_charge = models.IntegerField(blank=True, null=True)
    day_of_pay = models.IntegerField(blank=True, null=True)
    sms_username = models.CharField(max_length=255, db_collation='Thai_CI_AI', blank=True, null=True)
    sms_password = models.CharField(max_length=255, db_collation='Thai_CI_AI', blank=True, null=True)
    sms_sender = models.CharField(max_length=255, db_collation='Thai_CI_AI', blank=True, null=True)
    report_logo = models.TextField(db_collation='Thai_CI_AI', blank=True, null=True)
    report_header = models.TextField(db_collation='Thai_CI_AI', blank=True, null=True)
    certificate_fullname = models.CharField(max_length=255, db_collation='Thai_CI_AI', blank=True, null=True)
    certificate_position_1 = models.CharField(max_length=255, db_collation='Thai_CI_AI', blank=True, null=True)
    certificate_position_2 = models.CharField(max_length=255, db_collation='Thai_CI_AI', blank=True, null=True)
    certificate_position_3 = models.CharField(max_length=255, db_collation='Thai_CI_AI', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tcd_setting'


class TcdTitle(models.Model):
    name_th = models.CharField(max_length=100, db_collation='Thai_CI_AI')
    name_en = models.CharField(max_length=100, db_collation='Thai_CI_AI')

    class Meta:
        managed = False
        db_table = 'tcd_title'


class TcdPermission(models.Model):
    name = models.CharField(max_length=255, db_collation='Thai_CI_AI')
    worklist = models.CharField(max_length=1, db_collation='Thai_CI_AI')
    worklist_manage = models.CharField(max_length=1, db_collation='Thai_CI_AI')
    calendar = models.CharField(max_length=1, db_collation='Thai_CI_AI')
    calendar_manage = models.CharField(max_length=1, db_collation='Thai_CI_AI')
    work = models.CharField(max_length=1, db_collation='Thai_CI_AI')
    consult = models.CharField(max_length=1, db_collation='Thai_CI_AI')
    search = models.CharField(max_length=1, db_collation='Thai_CI_AI')
    report = models.CharField(max_length=1, db_collation='Thai_CI_AI')
    drop = models.CharField(max_length=1, db_collation='Thai_CI_AI')
    decrease = models.CharField(max_length=1, db_collation='Thai_CI_AI')
    appeal = models.CharField(max_length=1, db_collation='Thai_CI_AI')
    files = models.CharField(max_length=1, db_collation='Thai_CI_AI')
    contract = models.CharField(max_length=1, db_collation='Thai_CI_AI')
    stat = models.CharField(max_length=1, db_collation='Thai_CI_AI')
    email = models.CharField(max_length=1, db_collation='Thai_CI_AI')
    chat = models.CharField(max_length=1, db_collation='Thai_CI_AI')
    website = models.CharField(max_length=1, db_collation='Thai_CI_AI')
    data = models.CharField(max_length=1, db_collation='Thai_CI_AI')
    user = models.CharField(max_length=1, db_collation='Thai_CI_AI')
    matching = models.CharField(max_length=1, db_collation='Thai_CI_AI')
    project = models.CharField(max_length=1, db_collation='Thai_CI_AI')

    class Meta:
        managed = False
        db_table = 'tcd_permission'


class TcdUsers(models.Model):
    username = models.CharField(unique=True, max_length=255, db_collation='Thai_CI_AI')
    password = models.CharField(max_length=255, db_collation='Thai_CI_AI')
    identity_card_no = models.CharField(max_length=255, db_collation='Thai_CI_AI', blank=True, null=True)
    title = models.ForeignKey(TcdTitle, on_delete=models.CASCADE)
    firstname = models.CharField(max_length=255, db_collation='Thai_CI_AI')
    lastname = models.CharField(max_length=255, db_collation='Thai_CI_AI')
    email = models.CharField(max_length=255, db_collation='Thai_CI_AI')
    phone = models.CharField(max_length=255, db_collation='Thai_CI_AI', blank=True, null=True)
    position = models.CharField(max_length=255, db_collation='Thai_CI_AI')
    pic = models.TextField(db_collation='Thai_CI_AI', blank=True, null=True)
    checklogin = models.IntegerField()
    lastlogin = models.DateTimeField(blank=True, null=True)
    is_open = models.SmallIntegerField()
    permission = models.ForeignKey(TcdPermission, on_delete=models.CASCADE)

    class Meta:
        managed = False
        db_table = 'tcd_users'


class TcdCorporateType(models.Model):
    name_th = models.CharField(max_length=50, db_collation='Thai_CI_AI')
    name_en = models.CharField(max_length=50, db_collation='Thai_CI_AI')
    type = models.IntegerField()

    class Meta:
        managed = False
        db_table = 'tcd_corporate_type'


class TcdProvince(models.Model):
    name_th = models.CharField(max_length=255, db_collation='Thai_CI_AI')
    name_en = models.CharField(max_length=255, db_collation='Thai_CI_AI')
    two_digit = models.CharField(max_length=2, db_collation='Thai_CI_AI', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tcd_province'


class TcdAmphur(models.Model):
    name_th = models.CharField(max_length=255, db_collation='Thai_CI_AI')
    name_en = models.CharField(max_length=255, db_collation='Thai_CI_AI')
    province = models.ForeignKey(TcdProvince, on_delete=models.CASCADE)
    two_digit = models.CharField(max_length=2, db_collation='Thai_CI_AI', blank=True, null=True)
    four_digit = models.CharField(max_length=4, db_collation='Thai_CI_AI', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tcd_amphur'


class TcdDistrict(models.Model):
    amphur = models.ForeignKey(TcdAmphur, on_delete=models.CASCADE)
    name_th = models.CharField(max_length=255, db_collation='Thai_CI_AI')
    name_en = models.CharField(max_length=255, db_collation='Thai_CI_AI', blank=True, null=True)
    two_digit = models.CharField(max_length=2, db_collation='Thai_CI_AI', blank=True, null=True)
    six_digit = models.CharField(max_length=4, db_collation='Thai_CI_AI', blank=True, null=True)
    zipcode = models.IntegerField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tcd_district'


class TcdPersonalGeneralData(models.Model):
    guid = models.CharField(max_length=255, db_collation='Thai_CI_AI')
    picture_name = models.CharField(max_length=255, db_collation='Thai_CI_AI')
    title = models.ForeignKey(TcdTitle, on_delete=models.CASCADE)
    first_name = models.CharField(max_length=255, db_collation='Thai_CI_AI')
    last_name = models.CharField(max_length=255, db_collation='Thai_CI_AI')
    first_name_en = models.CharField(max_length=255, db_collation='Thai_CI_AI', blank=True, null=True)
    last_name_en = models.CharField(max_length=255, db_collation='Thai_CI_AI', blank=True, null=True)
    birth_date = models.DateTimeField()
    identity_card_no = models.CharField(unique=True, max_length=255, db_collation='Thai_CI_AI')
    is_male = models.SmallIntegerField()
    is_thai_nation = models.SmallIntegerField()
    other_nation = models.CharField(max_length=255, db_collation='Thai_CI_AI', blank=True, null=True)
    thai_lang = models.SmallIntegerField()
    eng_lang = models.SmallIntegerField()
    other_lang = models.CharField(max_length=255, db_collation='Thai_CI_AI', blank=True, null=True)
    address = models.CharField(max_length=2000, db_collation='Thai_CI_AI', blank=True, null=True)
    province = models.ForeignKey(TcdProvince, on_delete=models.CASCADE, related_name='personal_current_province')
    amphur = models.ForeignKey(TcdAmphur, on_delete=models.CASCADE, related_name='personal_current_amphur')
    district = models.ForeignKey(TcdDistrict, on_delete=models.CASCADE, related_name='personal_current_district')
    zipcode = models.CharField(max_length=255, db_collation='Thai_CI_AI', blank=True, null=True)
    card_address = models.CharField(max_length=2000, db_collation='Thai_CI_AI', blank=True, null=True)
    card_province = models.ForeignKey(TcdProvince, on_delete=models.CASCADE, related_name='personal_card_province')
    card_amphur = models.ForeignKey(TcdAmphur, on_delete=models.CASCADE, related_name='personal_card_amphur')
    card_district = models.ForeignKey(TcdDistrict, on_delete=models.CASCADE, related_name='personal_card_district')
    card_zipcode = models.CharField(max_length=255, db_collation='Thai_CI_AI', blank=True, null=True)
    telephone_no = models.CharField(max_length=255, db_collation='Thai_CI_AI', blank=True, null=True)
    mobile_no = models.CharField(max_length=255, db_collation='Thai_CI_AI', blank=True, null=True)
    fax_no = models.CharField(max_length=255, db_collation='Thai_CI_AI', blank=True, null=True)
    email = models.CharField(max_length=255, db_collation='Thai_CI_AI', blank=True, null=True)
    web_site = models.CharField(max_length=255, db_collation='Thai_CI_AI', blank=True, null=True)
    receive_by_email = models.SmallIntegerField(blank=True, null=True)
    receive_by_post = models.SmallIntegerField(blank=True, null=True)
    is_main_scholar = models.SmallIntegerField()
    experience_year = models.IntegerField(blank=True, null=True)
    work_experience = models.IntegerField(blank=True, null=True)
    send_doc = models.CharField(max_length=1, db_collation='Thai_CI_AI', blank=True, null=True)
    user_consult = models.ForeignKey(TcdUserConsult, on_delete=models.CASCADE)
    first_register_date = models.DateTimeField(blank=True, null=True)
    register_expire_date = models.DateTimeField(blank=True, null=True)
    register_no = models.CharField(max_length=255, db_collation='Thai_CI_AI', blank=True, null=True)
    rating = models.CharField(max_length=1, db_collation='Thai_CI_AI', blank=True, null=True)
    sector = models.TextField(db_collation='Thai_CI_AI', blank=True, null=True)
    skill = models.CharField(max_length=250, db_collation='Thai_CI_AI', blank=True, null=True)
    register_type = models.IntegerField()
    send_date = models.DateTimeField(blank=True, null=True)
    send_to_approve = models.SmallIntegerField()
    is_approve = models.SmallIntegerField()
    approver_id = models.IntegerField(blank=True, null=True)
    approver_name = models.CharField(max_length=255, db_collation='Thai_CI_AI', blank=True, null=True)
    comment = models.TextField(db_collation='Thai_CI_AI', blank=True, null=True)
    last_check_date = models.DateTimeField(blank=True, null=True)
    history_is_approve = models.SmallIntegerField()
    history_approver_id = models.IntegerField(blank=True, null=True)
    history_approver_name = models.CharField(max_length=255, db_collation='Thai_CI_AI', blank=True, null=True)
    history_comment = models.TextField(db_collation='Thai_CI_AI', blank=True, null=True)
    history_last_check_date = models.DateTimeField(blank=True, null=True)
    register_is_approve = models.SmallIntegerField()
    register_approver_id = models.IntegerField(blank=True, null=True)
    register_approver_name = models.CharField(max_length=255, db_collation='Thai_CI_AI', blank=True, null=True)
    register_approve_date = models.DateTimeField(blank=True, null=True)
    register_stamp_date = models.DateTimeField(blank=True, null=True)
    scholar_hire_type_id = models.DecimalField(max_digits=19, decimal_places=0, blank=True, null=True)
    scholar_type_id = models.DecimalField(max_digits=19, decimal_places=0, blank=True, null=True)
    service_skill_approver_id = models.IntegerField(blank=True, null=True)
    service_skill_approver_name = models.CharField(max_length=255, db_collation='Thai_CI_AI', blank=True, null=True)
    service_skill_is_approve = models.SmallIntegerField()
    service_skill_last_check_date = models.DateTimeField(blank=True, null=True)
    is_un_register = models.SmallIntegerField()
    un_register_id = models.IntegerField(blank=True, null=True)
    un_register_name = models.CharField(max_length=255, db_collation='Thai_CI_AI', blank=True, null=True)
    un_register_comment = models.TextField(db_collation='Thai_CI_AI', blank=True, null=True)
    un_register_date = models.DateTimeField(blank=True, null=True)
    is_open_edit = models.SmallIntegerField()
    open_edit_id = models.IntegerField(blank=True, null=True)
    open_edit_name = models.CharField(max_length=255, db_collation='Thai_CI_AI', blank=True, null=True)
    open_edit_date = models.DateTimeField(blank=True, null=True)
    open_edit_worklist_id = models.IntegerField(blank=True, null=True)
    service_skill_comment = models.TextField(db_collation='Thai_CI_AI', blank=True, null=True)
    is_re_new = models.SmallIntegerField()
    renew_date = models.DateTimeField(blank=True, null=True)
    is_adding_project = models.SmallIntegerField()
    is_check_complete = models.SmallIntegerField()
    adding_project_date = models.DateTimeField(blank=True, null=True)
    flow_state = models.IntegerField(blank=True, null=True)
    request_type = models.IntegerField(blank=True, null=True)
    is_pending = models.CharField(max_length=1, db_collation='Thai_CI_AI', blank=True, null=True)
    display_sector = models.SmallIntegerField(blank=True, null=True)
    profession_name = models.CharField(max_length=255, db_collation='Thai_CI_AI', blank=True, null=True)
    profession_no = models.CharField(max_length=255, db_collation='Thai_CI_AI', blank=True, null=True)
    check_add_date = models.DateTimeField(blank=True, null=True)
    age_consultant = models.CharField(max_length=255, db_collation='Thai_CI_AI', blank=True, null=True)
    age_experience_year = models.CharField(max_length=255, db_collation='Thai_CI_AI', blank=True, null=True)
    age_work_experience = models.CharField(max_length=255, db_collation='Thai_CI_AI', blank=True, null=True)
    purpose_id = models.IntegerField(blank=True, null=True)
    is_active = models.CharField(max_length=1, db_collation='Thai_CI_AI', blank=True, null=True)
    is_decrease = models.BooleanField()
    decrease_id = models.IntegerField(blank=True, null=True)
    decrease_rating = models.IntegerField(blank=True, null=True)
    is_blacklist = models.BooleanField()

    class Meta:
        managed = False
        db_table = 'tcd_personal_general_data'


class TcdCorporateGeneralData(models.Model):
    tax_no = models.CharField(max_length=50, db_collation='Thai_CI_AI', blank=True, null=True)
    tax_no_new = models.CharField(unique=True, max_length=50, db_collation='Thai_CI_AI', blank=True, null=True)
    corporate_profit_id = models.IntegerField(blank=True, null=True)
    name = models.CharField(max_length=300, db_collation='Thai_CI_AI')
    name_en = models.CharField(max_length=300, db_collation='Thai_CI_AI', blank=True, null=True)
    code = models.CharField(max_length=255, db_collation='Thai_CI_AI')
    register_date = models.DateTimeField(blank=True, null=True)
    organize_document_no = models.CharField(max_length=255, db_collation='Thai_CI_AI')
    old_tax_no_new = models.CharField(max_length=50, db_collation='Thai_CI_AI', blank=True, null=True)
    old_name = models.CharField(max_length=300, db_collation='Thai_CI_AI', blank=True, null=True)
    old_name_en = models.CharField(max_length=300, db_collation='Thai_CI_AI', blank=True, null=True)
    old_corporate_profit_id = models.IntegerField(blank=True, null=True)
    old_register_date = models.DateTimeField(blank=True, null=True)
    change_corporate_profit_date = models.DateTimeField(blank=True, null=True)
    change_corporate_profit_worklist_id = models.IntegerField(blank=True, null=True)
    address = models.CharField(max_length=2000, db_collation='Thai_CI_AI')
    province = models.ForeignKey(TcdProvince, on_delete=models.CASCADE, related_name='corporate_province')
    amphur = models.ForeignKey(TcdAmphur, on_delete=models.CASCADE, related_name='corporate_amphur')
    district = models.ForeignKey(TcdDistrict, on_delete=models.CASCADE, related_name='corporate_district')
    zipcode = models.CharField(max_length=255, db_collation='Thai_CI_AI', blank=True, null=True)
    telephone_no = models.CharField(max_length=255, db_collation='Thai_CI_AI')
    mobile_no = models.CharField(max_length=255, db_collation='Thai_CI_AI')
    fax_no = models.CharField(max_length=255, db_collation='Thai_CI_AI')
    email = models.CharField(max_length=255, db_collation='Thai_CI_AI')
    web_site = models.CharField(max_length=255, db_collation='Thai_CI_AI')
    receive_by_post = models.SmallIntegerField()
    receive_by_email = models.SmallIntegerField()
    thai_lang = models.SmallIntegerField()
    eng_lang = models.SmallIntegerField()
    other_lang = models.CharField(max_length=255, db_collation='Thai_CI_AI', blank=True, null=True)
    send_doc = models.CharField(max_length=1, db_collation='Thai_CI_AI', blank=True, null=True)
    user_consult = models.ForeignKey(TcdUserConsult, on_delete=models.CASCADE)
    first_register_date = models.DateTimeField(blank=True, null=True)
    register_expire_date = models.DateTimeField(blank=True, null=True)
    register_no = models.CharField(max_length=255, db_collation='Thai_CI_AI')
    rating = models.CharField(max_length=1, db_collation='Thai_CI_AI', blank=True, null=True)
    sector = models.TextField(db_collation='Thai_CI_AI', blank=True, null=True)
    skill = models.CharField(max_length=250, db_collation='Thai_CI_AI', blank=True, null=True)
    register_type = models.IntegerField()
    send_date = models.DateTimeField(blank=True, null=True)
    send_to_approve = models.SmallIntegerField()
    is_approve = models.SmallIntegerField()
    approver_id = models.DecimalField(max_digits=19, decimal_places=0)
    approver_name = models.CharField(max_length=255, db_collation='Thai_CI_AI', blank=True, null=True)
    comment = models.TextField(db_collation='Thai_CI_AI', blank=True, null=True)
    last_check_date = models.DateTimeField(blank=True, null=True)
    register_is_approve = models.SmallIntegerField()
    register_approver_id = models.IntegerField(blank=True, null=True)
    register_approver_name = models.CharField(max_length=255, db_collation='Thai_CI_AI', blank=True, null=True)
    register_approve_date = models.DateTimeField(blank=True, null=True)
    register_stamp_date = models.DateTimeField(blank=True, null=True)
    un_register_id = models.IntegerField(blank=True, null=True)
    un_register_name = models.CharField(max_length=255, db_collation='Thai_CI_AI', blank=True, null=True)
    un_register_comment = models.TextField(db_collation='Thai_CI_AI', blank=True, null=True)
    is_un_register = models.SmallIntegerField()
    un_register_date = models.DateTimeField(blank=True, null=True)
    is_open_edit = models.SmallIntegerField()
    open_edit_id = models.IntegerField(blank=True, null=True)
    open_edit_name = models.CharField(max_length=255, db_collation='Thai_CI_AI', blank=True, null=True)
    open_edit_date = models.DateTimeField(blank=True, null=True)
    open_edit_worklist_id = models.IntegerField(blank=True, null=True)
    is_re_new = models.SmallIntegerField()
    renew_date = models.DateTimeField(blank=True, null=True)
    is_adding_project = models.SmallIntegerField()
    is_check_complete = models.SmallIntegerField()
    adding_project_date = models.DateTimeField(blank=True, null=True)
    flow_state = models.IntegerField(blank=True, null=True)
    request_type = models.IntegerField(blank=True, null=True)
    is_pending = models.CharField(max_length=1, db_collation='Thai_CI_AI', blank=True, null=True)
    display_sector = models.SmallIntegerField(blank=True, null=True)
    profession_name = models.CharField(max_length=255, db_collation='Thai_CI_AI', blank=True, null=True)
    profession_no = models.CharField(max_length=255, db_collation='Thai_CI_AI', blank=True, null=True)
    check_add_date = models.DateTimeField(blank=True, null=True)
    purpose_id = models.IntegerField(blank=True, null=True)
    is_decrease = models.BooleanField()
    decrease_id = models.IntegerField(blank=True, null=True)
    decrease_rating = models.IntegerField(blank=True, null=True)
    is_blacklist = models.BooleanField()

    class Meta:
        managed = False
        db_table = 'tcd_corporate_general_data'


class TcdNoProfitGeneralData(models.Model):
    tax_no = models.CharField(max_length=50, db_collation='Thai_CI_AI')
    tax_no_new = models.CharField(unique=True, max_length=50, db_collation='Thai_CI_AI', blank=True, null=True)
    name = models.CharField(max_length=300, db_collation='Thai_CI_AI')
    name_en = models.CharField(max_length=300, db_collation='Thai_CI_AI', blank=True, null=True)
    code = models.CharField(max_length=255, db_collation='Thai_CI_AI')
    register_date = models.DateTimeField(blank=True, null=True)
    organize_document_no = models.CharField(max_length=255, db_collation='Thai_CI_AI')
    address = models.CharField(max_length=2000, db_collation='Thai_CI_AI')
    province = models.ForeignKey(TcdProvince, on_delete=models.CASCADE, related_name='noprofit_province')
    amphur = models.ForeignKey(TcdAmphur, on_delete=models.CASCADE, related_name='noprofit_amphur')
    district = models.ForeignKey(TcdDistrict, on_delete=models.CASCADE, related_name='noprofit_district')
    zipcode = models.CharField(max_length=255, db_collation='Thai_CI_AI', blank=True, null=True)
    telephone_no = models.CharField(max_length=255, db_collation='Thai_CI_AI')
    mobile_no = models.CharField(max_length=255, db_collation='Thai_CI_AI')
    fax_no = models.CharField(max_length=255, db_collation='Thai_CI_AI')
    email = models.CharField(max_length=255, db_collation='Thai_CI_AI')
    web_site = models.CharField(max_length=255, db_collation='Thai_CI_AI')
    receive_by_post = models.SmallIntegerField()
    receive_by_email = models.SmallIntegerField()
    thai_lang = models.SmallIntegerField()
    eng_lang = models.SmallIntegerField()
    other_lang = models.CharField(max_length=255, db_collation='Thai_CI_AI', blank=True, null=True)
    send_doc = models.CharField(max_length=1, db_collation='Thai_CI_AI', blank=True, null=True)
    user_consult_id = models.DecimalField(max_digits=19, decimal_places=0)
    first_register_date = models.DateTimeField(blank=True, null=True)
    register_expire_date = models.DateTimeField(blank=True, null=True)
    register_no = models.CharField(max_length=255, db_collation='Thai_CI_AI')
    rating = models.CharField(max_length=1, db_collation='Thai_CI_AI', blank=True, null=True)
    sector = models.TextField(db_collation='Thai_CI_AI', blank=True, null=True)
    skill = models.CharField(max_length=250, db_collation='Thai_CI_AI', blank=True, null=True)
    register_type = models.IntegerField()
    send_date = models.DateTimeField(blank=True, null=True)
    send_to_approve = models.SmallIntegerField()
    is_approve = models.SmallIntegerField()
    approver_id = models.DecimalField(max_digits=19, decimal_places=0)
    approver_name = models.CharField(max_length=255, db_collation='Thai_CI_AI', blank=True, null=True)
    comment = models.TextField(db_collation='Thai_CI_AI', blank=True, null=True)
    last_check_date = models.DateTimeField(blank=True, null=True)
    register_is_approve = models.SmallIntegerField()
    register_approver_id = models.IntegerField(blank=True, null=True)
    register_approver_name = models.CharField(max_length=255, db_collation='Thai_CI_AI', blank=True, null=True)
    register_approve_date = models.DateTimeField(blank=True, null=True)
    register_stamp_date = models.DateTimeField(blank=True, null=True)
    is_un_register = models.SmallIntegerField()
    un_register_id = models.IntegerField(blank=True, null=True)
    un_register_name = models.CharField(max_length=255, db_collation='Thai_CI_AI', blank=True, null=True)
    un_register_comment = models.TextField(db_collation='Thai_CI_AI', blank=True, null=True)
    un_register_date = models.DateTimeField(blank=True, null=True)
    is_open_edit = models.SmallIntegerField()
    open_edit_id = models.IntegerField(blank=True, null=True)
    open_edit_name = models.CharField(max_length=255, db_collation='Thai_CI_AI', blank=True, null=True)
    open_edit_date = models.DateTimeField(blank=True, null=True)
    open_edit_worklist_id = models.IntegerField(blank=True, null=True)
    is_re_new = models.SmallIntegerField()
    renew_date = models.DateTimeField(blank=True, null=True)
    is_adding_project = models.SmallIntegerField()
    is_check_complete = models.SmallIntegerField()
    adding_project_date = models.DateTimeField(blank=True, null=True)
    flow_state = models.IntegerField(blank=True, null=True)
    request_type = models.IntegerField(blank=True, null=True)
    is_pending = models.CharField(max_length=1, db_collation='Thai_CI_AI', blank=True, null=True)
    display_sector = models.SmallIntegerField(blank=True, null=True)
    profession_name = models.CharField(max_length=255, db_collation='Thai_CI_AI', blank=True, null=True)
    profession_no = models.CharField(max_length=255, db_collation='Thai_CI_AI', blank=True, null=True)
    check_add_date = models.DateTimeField(blank=True, null=True)
    purpose_id = models.IntegerField(blank=True, null=True)
    is_decrease = models.BooleanField()
    decrease_id = models.IntegerField(blank=True, null=True)
    decrease_rating = models.IntegerField(blank=True, null=True)
    is_blacklist = models.BooleanField()

    class Meta:
        managed = False
        db_table = 'tcd_no_profit_general_data'


class TcdProject(models.Model):
    id = models.AutoField(primary_key=True)
    no = models.IntegerField()
    project_type_id = models.IntegerField()
    employer_type = models.IntegerField(blank=True, null=True)
    contract_type = models.IntegerField(blank=True, null=True)
    name = models.CharField(max_length=500, db_collation='Thai_CI_AI')
    customer = models.CharField(max_length=500, db_collation='Thai_CI_AI')
    contract_no = models.CharField(max_length=50, db_collation='Thai_CI_AI')
    start_date = models.DateTimeField()
    stop_date = models.DateTimeField()
    period = models.CharField(max_length=50, db_collation='Thai_CI_AI', blank=True, null=True)
    place = models.CharField(max_length=1000, db_collation='Thai_CI_AI')
    total_project_value = models.FloatField()
    total_contract_value = models.FloatField()
    purpost = models.CharField(max_length=8000, db_collation='Thai_CI_AI')
    activity = models.CharField(max_length=8000, db_collation='Thai_CI_AI')
    purpost_en = models.CharField(max_length=8000, db_collation='Thai_CI_AI', blank=True, null=True)
    activity_en = models.CharField(max_length=8000, db_collation='Thai_CI_AI', blank=True, null=True)
    last_check_date = models.DateTimeField(blank=True, null=True)
    personal_general_data_id = models.IntegerField(blank=True, null=True)
    corporate_general_data_id = models.IntegerField(blank=True, null=True)
    no_profit_general_data_id = models.IntegerField(blank=True, null=True)
    ability_level_approver_id = models.IntegerField()
    ability_level_approver_name = models.CharField(max_length=255, db_collation='Thai_CI_AI', blank=True, null=True)
    ability_level_is_approve = models.SmallIntegerField()
    ability_level_last_check_date = models.DateTimeField(blank=True, null=True)
    approver_id = models.IntegerField()
    approver_name = models.CharField(max_length=255, db_collation='Thai_CI_AI', blank=True, null=True)
    is_approve = models.SmallIntegerField()
    comment = models.TextField(db_collation='Thai_CI_AI', blank=True, null=True)
    man_month = models.IntegerField()
    is_not_complete = models.SmallIntegerField(blank=True, null=True)
    is_active = models.SmallIntegerField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tcd_project'


class TcdProjectSector(models.Model):
    project_id = models.IntegerField()
    sector_id = models.IntegerField()

    class Meta:
        managed = False
        db_table = 'tcd_project_sector'


class TcdProjectService(models.Model):
    ability_level_id = models.IntegerField(blank=True, null=True)
    project_id = models.IntegerField()
    service_id = models.IntegerField()

    class Meta:
        managed = False
        db_table = 'tcd_project_service'


class TcdProjectSkill(models.Model):
    project_id = models.IntegerField()
    skill_id = models.IntegerField()
    project_sector_id = models.IntegerField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tcd_project_skill'


class TcdCalendar(models.Model):
    calendar_date = models.DateField()
    calendar_detail = models.CharField(max_length=255, db_collation='Thai_CI_AI')
    calendar_status = models.DecimalField(max_digits=19, decimal_places=0)

    class Meta:
        managed = False
        db_table = 'tcd_calendar'


class TcdNewscategory(models.Model):
    name = models.CharField(max_length=255, db_collation='Thai_CI_AI', blank=True, null=True)
    order = models.IntegerField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tcd_newscategory'


class TcdNews(models.Model):
    id = models.IntegerField(primary_key=True)
    name = models.CharField(max_length=255, db_collation='Thai_CI_AI')
    detail = models.TextField(db_collation='Thai_CI_AI', blank=True, null=True)
    thumb = models.TextField(db_collation='Thai_CI_AI', blank=True, null=True)
    video = models.CharField(max_length=255, db_collation='Thai_CI_AI', blank=True, null=True)
    newscategory_id = models.IntegerField()
    count = models.IntegerField()
    createdate = models.DateTimeField()
    createuser = models.IntegerField()
    status = models.CharField(max_length=1, db_collation='Thai_CI_AI')

    class Meta:
        managed = False
        db_table = 'tcd_news'


class TcdNewssrc(models.Model):
    type = models.CharField(max_length=50, db_collation='Thai_CI_AI')
    name = models.CharField(max_length=255, db_collation='Thai_CI_AI', blank=True, null=True)
    src = models.TextField(db_collation='Thai_CI_AI')
    thumb = models.TextField(db_collation='Thai_CI_AI', blank=True, null=True)
    news_id = models.IntegerField()

    class Meta:
        managed = False
        db_table = 'tcd_newssrc'

