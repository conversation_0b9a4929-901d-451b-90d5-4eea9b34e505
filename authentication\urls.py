from django.urls import path, include
from . import views
from rest_framework.routers import DefaultRouter
app_name = 'authentication'

# Create separate routers for Member and Consultant
member_router = DefaultRouter()
member_router.register("", views.MemberView, basename="member")

consultant_router = DefaultRouter()
consultant_router.register("", views.ConsultantView, basename="consultant")

# Create routers for Master Data
member_type_router = DefaultRouter()
member_type_router.register("", views.TcdAppMasMemberTypeViewSet, basename="member-type")

government_sector_router = DefaultRouter()
government_sector_router.register("", views.TcdAppMasGovernmentSectorViewSet, basename="government-sector")

ministry_router = DefaultRouter()
ministry_router.register("", views.TcdAppMasMinistryViewSet, basename="ministry")

department_router = DefaultRouter()
department_router.register("", views.TcdAppMasDepartmentViewSet, basename="department")

urlpatterns = [
    # Staff Login
    path('staff/login/', views.staff_login, name='staff_login'),
    
    # Token Management
    path('refresh/', views.refresh_token, name='refresh_token'),
    path('logout/', views.logout, name='logout'),
    path('revoke/', views.revoke_token, name='revoke_token'),

    # Profile Management
    path('profile/', views.get_user_profile, name='get_user_profile'),

    # Upload profile picture endpoint (standalone)
    path('member/upload-profile-picture/', views.upload_profile_picture, name='upload_profile_picture'),

    # OTP
    path('otp/generate/', views.generate_otp, name='generate_otp'),
    path('otp/verify/', views.verify_otp, name='verify_otp'),
    path('otp/validate/', views.validate_otp_token, name='validate_otp_token'),

    # Password Reset
    path('password-reset/request/', views.request_password_reset, name='request_password_reset'),
    path('password-reset/verify-otp/', views.verify_password_reset_otp, name='verify_password_reset_otp'),
    path('password-reset/update-password/', views.update_password_reset, name='update_password_reset'),

    # Change Password
    path('member/change-password/', views.change_password, name='change_password'),

    # Delete Member
    path('member/delete/', views.delete_member, name='delete_member'),
    
    # Update Member Info
    path('member/update-info/', views.update_member_info, name='update_member_info'),
    
    # Update Member Language
    path('member/update-lang/', views.update_member_lang, name='update_member_lang'),

    # Use separate routers
    path("member/", include(member_router.urls)),
    path("consultant/", include(consultant_router.urls)),
    
    # Master Data routes
    path("mas/member-type/", include(member_type_router.urls)),
    path("mas/government-sector/", include(government_sector_router.urls)),
    path("mas/ministry/", include(ministry_router.urls)),
    path("mas/department/", include(department_router.urls)),
    
    # Token App Update endpoint (unified)
    path('update-token/', views.update_token_app, name='update_token_app'),
    
    # Login by ThaID
    path('login-by-code/', views.login_by_thaid_code, name='login_by_thaid_code'),
    
    # Set PID
    path('set-member-pid/', views.set_pid, name='set_pid'),

    # App Usage Tracking
    path('track-app-usage/', views.track_app_usage, name='track_app_usage'),
]
