"""
Django settings for MCDC project.

Generated by 'django-admin startproject' using Django 5.2.1.

For more information on this file, see
https://docs.djangoproject.com/en/5.2/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/5.2/ref/settings/
"""

from pathlib import Path
import os
from datetime import timedelta

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent


# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/5.2/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = "django-insecure-j+sdvm5lt8xdd_8ea2mfp@rd&p%25h8@pw@$ompx)y_!a_r4p1"
VALID_API_KEYS = os.environ.get('X-API-Key', '')
API_VERSION = "v.0.0.1"

# JWT RSA Keys for RS256 algorithm
JWT_PRIVATE_KEY_PATH = os.path.join(BASE_DIR, 'keys', 'private_key.pem')
JWT_PUBLIC_KEY_PATH = os.path.join(BASE_DIR, 'keys', 'public_key.pem')

# Load RSA keys
def load_rsa_key(key_path, is_private=True):
    """Load RSA key from file"""
    try:
        with open(key_path, 'rb') as key_file:
            if is_private:
                from cryptography.hazmat.primitives import serialization
                return serialization.load_pem_private_key(
                    key_file.read(),
                    password=None
                )
            else:
                from cryptography.hazmat.primitives import serialization
                return serialization.load_pem_public_key(key_file.read())
    except Exception as e:
        print(f"Error loading RSA key from {key_path}: {e}")
        return None

# Load keys at startup
JWT_PRIVATE_KEY = load_rsa_key(JWT_PRIVATE_KEY_PATH, is_private=True)
JWT_PUBLIC_KEY = load_rsa_key(JWT_PUBLIC_KEY_PATH, is_private=False)

# Fallback to HS256 if RSA keys are not available (for development/testing)
if JWT_PRIVATE_KEY is None or JWT_PUBLIC_KEY is None:
    print("Warning: RSA keys not found, falling back to HS256 algorithm for JWT")
    JWT_ALGORITHM = "HS256"
    JWT_SECRET_KEY = SECRET_KEY  # Use Django secret key as fallback
else:
    JWT_ALGORITHM = "RS256"
    JWT_SECRET_KEY = None

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = True

ALLOWED_HOSTS = ['*']


# Application definition

INSTALLED_APPS = [
    "daphne",  # Add daphne at the top for ASGI support
    "django.contrib.admin",
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.messages",
    "django.contrib.staticfiles",
    "rest_framework",
    "rest_framework_simplejwt",
    "rest_framework_simplejwt.token_blacklist",
    "drf_spectacular",
    "channels",  # Add channels for WebSocket support
    "corsheaders",  # Add CORS support
    "MCDC",
    "authentication",
    "chat",
    "search",
    "project",
    "faq",
    "payment",
    "tracking",
    "dashboard",
    "notification",
    "surveys",
    "documents",
]

MIDDLEWARE = [
    "corsheaders.middleware.CorsMiddleware",  # Add CORS middleware at the top
    "django.middleware.security.SecurityMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "MCDC.middleware.JWTUserMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
    "MCDC.middleware.APIKeyValidationMiddleware",
    "MCDC.middleware.APIVersionMiddleware",
]

ROOT_URLCONF = "MCDC.urls"

APPEND_SLASH = True

TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": [BASE_DIR / 'templates'],
        "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
            ],
        },
    },
]

WSGI_APPLICATION = "MCDC.wsgi.application"
ASGI_APPLICATION = "MCDC.asgi.application"


# Database
# https://docs.djangoproject.com/en/5.2/ref/settings/#databases

DATABASES = {
    'default': {
        'ENGINE': 'mssql',
        'NAME': os.environ.get('DB_SERVICE', ''),
        'USER': os.environ.get('DB_USER', ''),
        'PASSWORD': os.environ.get('DB_PASSWORD', ''),
        'HOST': os.environ.get('DB_HOST', ''),
        'PORT': os.environ.get('DB_PORT', ''),
        'OPTIONS': {
            'driver': 'ODBC Driver 17 for SQL Server',
            'TrustServerCertificate': True,
            'extra_params': 'SET time_zone = "+07:00"',
        },
    }
}


# Password validation
# https://docs.djangoproject.com/en/5.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        "NAME": "django.contrib.auth.password_validation.UserAttributeSimilarityValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.MinimumLengthValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.CommonPasswordValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.NumericPasswordValidator",
    },
]


# Internationalization
# https://docs.djangoproject.com/en/5.2/topics/i18n/

LANGUAGE_CODE = "en-us"

TIME_ZONE = "Asia/Bangkok"

USE_I18N = True

USE_TZ = False
TIME_ZONE = 'Asia/Bangkok'


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/5.2/howto/static-files/

STATIC_URL = "static/"
STATIC_ROOT = os.path.join(BASE_DIR, 'static')

# Media files
MEDIA_PREFIX = os.environ.get('MEDIA_PREFIX', '')
BASE_URL = os.environ.get('BASE_URL', 'http://localhost:8000')  # Changed default port to 8000
MEDIA_URL = BASE_URL + MEDIA_PREFIX
UPLOAD_DIR = os.environ.get('UPLOAD_DIR', 'uploads')
MEDIA_ROOT = os.path.join(BASE_DIR, UPLOAD_DIR)
CHAT_SUB_DIR = os.environ.get('CHAT_SUB_DIR', '')
PAYMENT_SUB_DIR = os.environ.get('PAYMENT_SUB_DIR', '')
DOCUMENT_SUB_DIR = os.environ.get('DOCUMENT_SUB_DIR', '')
DASHBOARD_SUB_DIR = os.environ.get('DASHBOARD_SUB_DIR', '')
WORKLIST_SUB_DIR = os.environ.get('WORKLIST_SUB_DIR', '')
MATCHING_SUB_DIR = os.environ.get('MATCHING_SUB_DIR', '')
BASE_FILE_URL = os.environ.get('BASE_FILE_URL', 'http://127.0.0.1:8080/')
THAID_BASE_AUTH_URL = os.environ.get('THAID_BASE_AUTH_URL', 'https://imauth.bora.dopa.go.th/api/v2/oauth2/')
THAID_CALLBACK_URL = os.environ.get('THAID_CALLBACK_URL', 'https://pdmoapi.wewasanad.org/api/callback/thaid')
THAID_CLIENT_ID = os.environ.get('THAID_CLIENT_ID', '')
THAID_CLIENT_SECRET = os.environ.get('THAID_CLIENT_SECRET', '')

# WebSocket Configuration
WEBSOCKET_HOST = os.environ.get('WEBSOCKET_HOST', 'localhost')
WEBSOCKET_PORT = os.environ.get('WEBSOCKET_PORT', '8080')
WEBSOCKET_PROTOCOL = os.environ.get('WEBSOCKET_PROTOCOL', 'ws')  # 'ws' or 'wss'
WEBSOCKET_BASE_URL = f"{WEBSOCKET_PROTOCOL}://{WEBSOCKET_HOST}:{WEBSOCKET_PORT}"

# Default primary key field type
# https://docs.djangoproject.com/en/5.2/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = "django.db.models.BigAutoField"

# REST Framework settings
REST_FRAMEWORK = {
    'DEFAULT_PERMISSION_CLASSES': [
        'rest_framework.permissions.IsAuthenticated',
    ],
    'DEFAULT_AUTHENTICATION_CLASSES': [
        'authentication.backends.CustomJWTAuthentication',
        'rest_framework.authentication.SessionAuthentication',
    ],
    'DEFAULT_FILTER_BACKENDS': [
        'django_filters.rest_framework.DjangoFilterBackend',
        'rest_framework.filters.OrderingFilter',
        'rest_framework.filters.SearchFilter',
    ],
    'DEFAULT_SCHEMA_CLASS': 'drf_spectacular.openapi.AutoSchema',
    'DEFAULT_RENDERER_CLASSES': [
        'rest_framework.renderers.JSONRenderer',
        'rest_framework.renderers.BrowsableAPIRenderer',
    ],
    'EXCEPTION_HANDLER': 'utils.exceptions.custom_exception_handler',
    'USER_DETAILS_SERIALIZER': 'authentication.serializers.CustomUserDetailsSerializer',
}

AUTHENTICATION_BACKENDS = [
    'authentication.auth.MCDCUserModelBackend',
    'django.contrib.auth.backends.ModelBackend',
]

# JWT Settings - Configurable via environment variables
JWT_ACCESS_TOKEN_MINUTES = int(os.environ.get('JWT_ACCESS_TOKEN_MINUTES', 15))  # Default: 15 minutes
JWT_REFRESH_TOKEN_DAYS = int(os.environ.get('JWT_REFRESH_TOKEN_DAYS', 1))  # Default: 1 day

SIMPLE_JWT = {
    'ACCESS_TOKEN_LIFETIME': timedelta(minutes=JWT_ACCESS_TOKEN_MINUTES),
    'REFRESH_TOKEN_LIFETIME': timedelta(days=JWT_REFRESH_TOKEN_DAYS),
    "ROTATE_REFRESH_TOKENS": True,
    "BLACKLIST_AFTER_ROTATION": True,
    "UPDATE_LAST_LOGIN": False,
    "ALGORITHM": JWT_ALGORITHM,
    "SIGNING_KEY": JWT_PRIVATE_KEY if JWT_ALGORITHM == "RS256" else JWT_SECRET_KEY,
    "VERIFYING_KEY": JWT_PUBLIC_KEY if JWT_ALGORITHM == "RS256" else JWT_SECRET_KEY,
    "AUTH_HEADER_TYPES": ("Bearer",),
    "TOKEN_TYPE_CLAIM": "token_type",
    "JTI_CLAIM": "jti",
    'USER_ID_FIELD': 'id',
    'USER_ID_CLAIM': 'user_id',
    'TOKEN_USER_CLASS': 'authentication.models.TcdUserConsult',
    'AUTH_TOKEN_CLASSES': ('rest_framework_simplejwt.tokens.AccessToken',),
    # Custom table names for token blacklist (Alternative method)
    'BLACKLIST_TOKEN_MODEL': 'authentication.models.CustomBlacklistedToken',
    'OUTSTANDING_TOKEN_MODEL': 'authentication.models.CustomOutstandingToken',
}

# Cache configuration for token tracking and channels
CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
        'LOCATION': 'unique-snowflake',
    }
}

# Cache time to live is 24 hours (in seconds) to match refresh token lifetime
CACHE_TTL = 60 * 60 * 24

# Channels configuration (In-Memory for development/simple deployment)
CHANNEL_LAYERS = {
    'default': {
        'BACKEND': 'channels.layers.InMemoryChannelLayer',
    },
}

# Add Spectacular settings at the end of the file
SPECTACULAR_SETTINGS = {
    'TITLE': 'MCDC API',
    'DESCRIPTION': 'API for MCDC',
    'VERSION': API_VERSION,
    'SERVE_INCLUDE_SCHEMA': False,
    'DISABLE_ERRORS_AND_WARNINGS': True,
}

# Logging configuration
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {name} {message}',
            'style': '{',
        },
        'simple': {
            'format': '{levelname} {name} {message}',
            'style': '{',
        },
    },
    'handlers': {
        'console': {
            'level': os.environ.get('LOG_LEVEL', 'DEBUG'),
            'class': 'logging.StreamHandler',
            'formatter': 'simple'
        },
        'file': {
            'level': os.environ.get('LOG_LEVEL', 'DEBUG'),
            'class': 'logging.FileHandler',
            'filename': 'logs/app.log',
            'formatter': 'verbose',
            'encoding': 'utf-8',
        },
    },
    'loggers': {
        '': {  # Root logger
            'handlers': ['console', 'file'],
            'level': os.environ.get('LOG_LEVEL', 'DEBUG'),
            'propagate': False,
        },
        'django': {
            'handlers': ['console', 'file'],
            'level': os.environ.get('LOG_LEVEL', 'INFO'),
            'propagate': False,
        },
        'authentication': {
            'handlers': ['console', 'file'],
            'level': 'DEBUG',
            'propagate': False,
        },
        'utils': {
            'handlers': ['console', 'file'],
            'level': 'DEBUG',
            'propagate': False,
        },
    },
}

# AUTH_USER_MODEL = 'users.User'

# CORS settings
CORS_ALLOW_ALL_ORIGINS = True  # For development only, set to False in production
# For production, specify allowed origins:
# CORS_ALLOWED_ORIGINS = [
#     "https://example.com",
#     "https://sub.example.com",
#     "http://localhost:8080",
#     "http://127.0.0.1:9000",
# ]

# Additional CORS settings
CORS_ALLOW_METHODS = [
    "DELETE",
    "GET",
    "OPTIONS",
    "PATCH",
    "POST",
    "PUT",
]

CORS_ALLOW_HEADERS = [
    "accept",
    "accept-encoding",
    "authorization",
    "content-type",
    "dnt",
    "origin",
    "user-agent",
    "x-csrftoken",
    "x-requested-with",
]

# API Key Validation Settings
API_KEY_VALIDATION_SETTINGS = {
    'ENABLED': True,
    'PROTECTED_PATHS': [
        '/api/',
        '/auth/',
    ],
    'EXEMPTED_PATHS': [
        '/static/',
        '/media/',
        '/files/',  # Add files path for uploaded files
        '/admin/',
        '/swagger/',
        '/redoc/',
        '/docs/',
        '/api/schema/',
        '/api/docs/',
        '/api/search/',  # Add search endpoints as public (no API key required)
        '/api/faq/',     # Add FAQ endpoints as public (no API key required)
        '/api/callback/',  # Add all callback endpoints as public (no API key required)
    ],
}