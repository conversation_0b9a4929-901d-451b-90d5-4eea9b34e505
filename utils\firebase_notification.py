# -*- coding: utf-8 -*-
"""
Firebase Cloud Messaging (FCM) Push Notification Utility
"""
import os
import json
import logging
from typing import List, Dict, Optional, Union

try:
    import firebase_admin
    from firebase_admin import credentials, messaging
    FIREBASE_AVAILABLE = True
except ImportError:
    FIREBASE_AVAILABLE = False
    logging.warning("Firebase Admin SDK not available. Install with: pip install firebase-admin")

logger = logging.getLogger(__name__)


class FirebaseNotificationService:
    """
    Service class for sending Firebase Cloud Messaging (FCM) push notifications
    using Firebase Admin SDK
    """
    
    def __init__(self):
        """
        Initialize Firebase notification service
        """
        self.initialized = False
        if FIREBASE_AVAILABLE:
            self._initialize_firebase()
    
    def _initialize_firebase(self):
        """
        Initialize Firebase Admin SDK
        """
        try:
            # Check if Firebase is already initialized
            if not firebase_admin._apps:
                # Get the path to the service account key file
                key_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'keys', 'cdc.json')
                
                if os.path.exists(key_path):
                    cred = credentials.Certificate(key_path)
                    firebase_admin.initialize_app(cred)
                    self.initialized = True
                    logger.info("Firebase Admin SDK initialized successfully")
                else:
                    logger.error(f"Firebase service account key file not found at: {key_path}")
            else:
                self.initialized = True
                logger.info("Firebase Admin SDK already initialized")
                
        except Exception as e:
            logger.error(f"Error initializing Firebase Admin SDK: {str(e)}")
            self.initialized = False
    
    def send_notification(
        self,
        device_tokens: Union[str, List[str]],
        title: str,
        body: str,
        data: Optional[Dict] = None,
        click_action: Optional[str] = None
    ) -> Dict:
        """
        Send push notification to device(s) using Firebase Admin SDK
        
        Args:
            device_tokens: Single token string or list of device tokens
            title: Notification title
            body: Notification body
            data: Additional data payload
            click_action: URL to open when notification is clicked
            
        Returns:
            dict: Response with success status and details
        """
        try:
            if not FIREBASE_AVAILABLE:
                return {
                    'success': False,
                    'error': 'Firebase Admin SDK not available',
                    'sent_count': 0,
                    'failed_count': 0
                }
            
            if not self.initialized:
                return {
                    'success': False,
                    'error': 'Firebase Admin SDK not initialized',
                    'sent_count': 0,
                    'failed_count': 0
                }
            
            # Convert single token to list
            if isinstance(device_tokens, str):
                device_tokens = [device_tokens]
            
            # Filter out empty tokens
            valid_tokens = [token for token in device_tokens if token and token.strip()]
            
            if not valid_tokens:
                return {
                    'success': False,
                    'error': 'No valid device tokens provided',
                    'sent_count': 0,
                    'failed_count': 0
                }
            
            sent_count = 0
            failed_count = 0
            errors = []
            
            # Send notifications to each token
            for token in valid_tokens:
                try:
                    # Prepare notification payload
                    notification = messaging.Notification(
                        title=title,
                        body=body
                    )
                    
                    # Prepare data payload
                    data_payload = data or {}
                    if click_action:
                        data_payload['click_action'] = click_action
                    
                    # Create message
                    message = messaging.Message(
                        notification=notification,
                        data=data_payload,
                        token=token,
                        android=messaging.AndroidConfig(
                            priority='high',
                            notification=messaging.AndroidNotification(
                                sound='default',
                                priority='high'
                            )
                        ),
                        apns=messaging.APNSConfig(
                            payload=messaging.APNSPayload(
                                aps=messaging.Aps(
                                    sound='default',
                                    badge=1
                                )
                            )
                        )
                    )
                    
                    # Send message
                    response = messaging.send(message)
                    sent_count += 1
                    logger.debug(f"Message sent successfully to token {token[:20]}...: {response}")
                    
                except Exception as e:
                    failed_count += 1
                    error_msg = str(e)
                    errors.append(f"Token {token[:20]}...: {error_msg}")
                    logger.error(f"Failed to send message to token {token[:20]}...: {error_msg}")
            
            logger.info(f"FCM notification sent - Success: {sent_count}, Failed: {failed_count}")
            
            return {
                'success': sent_count > 0,
                'sent_count': sent_count,
                'failed_count': failed_count,
                'errors': errors,
                'total_tokens': len(valid_tokens)
            }
            
        except Exception as e:
            logger.error(f"Error sending FCM notification: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'sent_count': 0,
                'failed_count': len(device_tokens) if isinstance(device_tokens, list) else 1
            }
    
    def send_project_matching_notification(
        self,
        device_token: str,
        project_name: str,
        consultant_name: str = None,
        project_id: int = None
    ) -> Dict:
        """
        Send project matching notification to project owner
        
        Args:
            device_token: Device token of project owner
            project_name: Name of the project
            consultant_name: Name of interested consultant (optional)
            project_id: ID of the project for deep linking
            
        Returns:
            dict: Response details
        """
        title = "ศูนย์ข้อมูลที่ปรึกษา"
        body = "ท่านมีที่ปรึกษาที่สนใจในโครงการที่ประกาศ"
        
        # Add project name to body if available
        if project_name:
            body = f"ท่านมีที่ปรึกษาที่สนใจในโครงการ \"{project_name}\""
        
        # Prepare data for deep linking
        data = {
            'type': 'project_matching',
            'action': 'view_matching_results'
        }
        
        if project_id:
            data['project_id'] = str(project_id)
        
        if consultant_name:
            data['consultant_name'] = consultant_name
        
        # URL for matching results page (adjust based on your mobile app routing)
        click_action = f"app://matching-results"
        if project_id:
            click_action = f"app://matching-results?project_id={project_id}"
        
        return self.send_notification(
            device_tokens=device_token,
            title=title,
            body=body,
            data=data,
            click_action=click_action
        )


# Global instance for easy access
firebase_service = FirebaseNotificationService()


def send_push_notification(
    device_tokens: Union[str, List[str]],
    title: str,
    body: str,
    data: Optional[Dict] = None,
    click_action: Optional[str] = None
) -> Dict:
    """
    Convenience function to send push notification
    
    Args:
        device_tokens: Single token string or list of device tokens
        title: Notification title
        body: Notification body
        data: Additional data payload
        click_action: URL to open when notification is clicked
        
    Returns:
        dict: Response with success status and details
    """
    return firebase_service.send_notification(
        device_tokens=device_tokens,
        title=title,
        body=body,
        data=data,
        click_action=click_action
    )


def send_project_matching_notification(
    device_token: str,
    project_name: str,
    consultant_name: str = None,
    project_id: int = None
) -> Dict:
    """
    Convenience function to send project matching notification
    
    Args:
        device_token: Device token of project owner
        project_name: Name of the project
        consultant_name: Name of interested consultant (optional)
        project_id: ID of the project for deep linking
        
    Returns:
        dict: Response details
    """
    return firebase_service.send_project_matching_notification(
        device_token=device_token,
        project_name=project_name,
        consultant_name=consultant_name,
        project_id=project_id
    )
