from rest_framework import serializers
from search.models import TcdSector


class DateRangeValidationSerializer(serializers.Serializer):
    """
    Serializer สำหรับตรวจสอบช่วงวันที่
    """
    start_date = serializers.DateField(required=True, help_text="วันที่เริ่มต้น")
    end_date = serializers.DateField(required=True, help_text="วันที่สิ้นสุด")

    def validate(self, data):
        """
        ตรวจสอบว่าวันที่สิ้นสุดไม่น้อยกว่าวันที่เริ่มต้น
        """
        start_date = data.get('start_date')
        end_date = data.get('end_date')

        if start_date and end_date:
            if end_date < start_date:
                raise serializers.ValidationError({
                    'end_date': 'วันที่ขึ้นทะเบียนสิ้นสุดห้ามเลือกวันที่น้อยกว่าวันที่ขึ้นทะเบียนเริ่มต้น'
                })

        return data


class ProjectCountRequestSerializer(serializers.Serializer):
    """
    Serializer for project count request parameters with dynamic filtering
    """
    project_name = serializers.CharField(required=False, allow_blank=True, max_length=500)
    organization_name = serializers.CharField(required=False, allow_blank=True, max_length=255)
    sector_ids = serializers.CharField(required=False, allow_blank=True)
    project_period_start = serializers.Date<PERSON>ield(required=False)
    project_period_end = serializers.DateField(required=False)
    announcement_start_date = serializers.DateField(required=False)
    announcement_end_date = serializers.DateField(required=False)

    def validate_sector_ids(self, value):
        """Validate and parse sector_ids from comma-separated string"""
        if not value or value.strip() == '':
            return []

        try:
            # Split by comma and convert to integers
            sector_ids = []
            for sector_id_str in value.split(','):
                sector_id_str = sector_id_str.strip()
                if sector_id_str:  # Skip empty strings
                    sector_id = int(sector_id_str)
                    if sector_id <= 0:
                        raise serializers.ValidationError("Sector IDs must be positive integers")
                    sector_ids.append(sector_id)
            return sector_ids
        except ValueError:
            raise serializers.ValidationError("Sector IDs must be comma-separated integers (e.g., '1,2,3')")

    def validate(self, data):
        """Cross-field validation"""
        # Validate date ranges
        if data.get('project_period_start') and data.get('project_period_end'):
            if data['project_period_start'] > data['project_period_end']:
                raise serializers.ValidationError({
                    'project_period_end': 'Project period end date must be after start date'
                })

        if data.get('announcement_start_date') and data.get('announcement_end_date'):
            if data['announcement_start_date'] > data['announcement_end_date']:
                raise serializers.ValidationError({
                    'announcement_end_date': 'Announcement end date must be after start date'
                })

        return data


class ProjectCountResponseSerializer(serializers.Serializer):
    """
    Serializer for project count response
    """
    count = serializers.IntegerField()
    filters_applied = serializers.DictField()


class ProjectListSerializer(serializers.Serializer):
    """
    Serializer for project list response with all required fields
    """
    project_name = serializers.CharField()
    organization_name = serializers.CharField()
    announcement_period = serializers.CharField()
    sectors = serializers.CharField()
    view_count = serializers.IntegerField()
    matching_result = serializers.FloatField(required=False, allow_null=True)


class ProjectSearchRequestSerializer(serializers.Serializer):
    """
    Enhanced serializer for project search request with comprehensive filtering
    """
    # Text search fields
    project_name = serializers.CharField(required=False, allow_blank=True, max_length=500)
    organization_name = serializers.CharField(required=False, allow_blank=True, max_length=255)
    keyword = serializers.CharField(required=False, allow_blank=True, max_length=255)

    # Sector filtering
    sector_ids = serializers.CharField(required=False, allow_blank=True)

    # Date range filtering
    project_period_start = serializers.DateField(required=False)
    project_period_end = serializers.DateField(required=False)
    announcement_start_date = serializers.DateField(required=False)
    announcement_end_date = serializers.DateField(required=False)

    # Advanced filtering
    min_view_count = serializers.IntegerField(required=False, min_value=0)
    max_view_count = serializers.IntegerField(required=False, min_value=0)
    has_matching_results = serializers.BooleanField(required=False)

    # Sorting options
    sort_by = serializers.ChoiceField(
        choices=[
            'id', '-id',
            'name', '-name',
            'start_date', '-start_date',
            'end_date', '-end_date',
            'view_count', '-view_count',
            'matching', '-matching',
            'create_date', '-create_date'
        ],
        required=False,
        default='-id'
    )

    def validate_sector_ids(self, value):
        """Validate and parse sector_ids from comma-separated string"""
        if not value or value.strip() == '':
            return []

        try:
            sector_ids = []
            for sector_id_str in value.split(','):
                sector_id_str = sector_id_str.strip()
                if sector_id_str:
                    sector_id = int(sector_id_str)
                    if sector_id <= 0:
                        raise serializers.ValidationError("Sector IDs must be positive integers")
                    sector_ids.append(sector_id)
            return sector_ids
        except ValueError:
            raise serializers.ValidationError("Sector IDs must be comma-separated integers (e.g., '1,2,3')")

    def validate(self, data):
        """Cross-field validation for enhanced project search"""
        # Validate date ranges
        if data.get('project_period_start') and data.get('project_period_end'):
            if data['project_period_start'] > data['project_period_end']:
                raise serializers.ValidationError({
                    'project_period_end': 'Project period end date must be after start date'
                })

        if data.get('announcement_start_date') and data.get('announcement_end_date'):
            if data['announcement_start_date'] > data['announcement_end_date']:
                raise serializers.ValidationError({
                    'announcement_end_date': 'Announcement end date must be after start date'
                })

        # Validate view count range
        if data.get('min_view_count') is not None and data.get('max_view_count') is not None:
            if data['min_view_count'] > data['max_view_count']:
                raise serializers.ValidationError({
                    'max_view_count': 'Maximum view count must be greater than or equal to minimum view count'
                })

        return data


class ProjectSearchResponseSerializer(serializers.Serializer):
    """
    Enhanced serializer for project search response with comprehensive data
    """
    # Project basic information
    project_id = serializers.IntegerField()
    project_name = serializers.CharField()
    organization_name = serializers.CharField()

    # Project details
    purpose = serializers.CharField(required=False, allow_null=True)
    activity = serializers.CharField(required=False, allow_null=True)
    reference = serializers.CharField(required=False, allow_null=True)
    keyword = serializers.CharField(required=False, allow_null=True)

    # Date information
    announcement_period = serializers.CharField()
    project_period = serializers.CharField()

    # Sector information
    sectors = serializers.CharField()
    sector_details = serializers.ListField(
        child=serializers.DictField(),
        required=False
    )

    # Statistics
    view_count = serializers.IntegerField()
    matching_result = serializers.FloatField(required=False, allow_null=True)

    # Additional metadata
    is_active = serializers.BooleanField()
    last_updated = serializers.DateTimeField(required=False, allow_null=True)


class ProjectDetailRequestSerializer(serializers.Serializer):
    """
    Serializer for project detail request parameters
    """
    project_id = serializers.IntegerField(min_value=1)


class SectorSkillSerializer(serializers.Serializer):
    """
    Serializer for sector and skill information
    """
    skill_code = serializers.CharField()
    skill_name_th = serializers.CharField()
    skill_name_en = serializers.CharField()
    skill_display = serializers.CharField()


class SectorDetailSerializer(serializers.Serializer):
    """
    Serializer for sector detail information
    """
    sector_code = serializers.CharField()
    sector_name_th = serializers.CharField()
    sector_name_en = serializers.CharField()
    sector_display = serializers.CharField()
    skills = SectorSkillSerializer(many=True)


class ServiceDetailSerializer(serializers.Serializer):
    """
    Serializer for service information
    """
    service_code = serializers.CharField()
    service_name_th = serializers.CharField()
    service_name_en = serializers.CharField()
    service_display = serializers.CharField()


class OrganizationDetailSerializer(serializers.Serializer):
    """
    Serializer for organization information
    """
    name = serializers.CharField()
    phone = serializers.CharField()
    email = serializers.CharField()
    type = serializers.CharField()
    website = serializers.CharField()


class ContactButtonSerializer(serializers.Serializer):
    """
    Serializer for contact button information
    """
    show = serializers.BooleanField()
    text = serializers.CharField(required=False)
    sent = serializers.BooleanField(required=False)


class ProjectDetailResponseSerializer(serializers.Serializer):
    """
    Serializer for project detail response
    """
    # Basic project information
    project_name = serializers.CharField()
    view_count = serializers.IntegerField()
    purpose = serializers.CharField()
    activity_scope = serializers.CharField()
    project_period = serializers.CharField()
    announcement_period = serializers.CharField()
    keyword = serializers.CharField()
    document_url = serializers.CharField(required=False, allow_null=True, help_text="ปุ่ม ดาวน์โหลดเอกสาร")

    # Organization information
    organization = OrganizationDetailSerializer()

    # Master data information
    consultant_experience = serializers.CharField()
    matching_result_type = serializers.CharField()
    contract_value = serializers.CharField()
    project_count = serializers.CharField()
    project_sector_count = serializers.CharField()
    project_type = serializers.CharField()
    consultant_count = serializers.CharField()
    consultant_type = serializers.CharField()
    consultant_level = serializers.CharField()
    registration_certificate = serializers.CharField()

    # Detailed information
    sectors = SectorDetailSerializer(many=True)
    services = ServiceDetailSerializer(many=True)

    # Matching information (for consultants only)
    matching_result = serializers.FloatField(required=False, allow_null=True)
    contact_button = ContactButtonSerializer()


class ProjectViewCountResponseSerializer(serializers.Serializer):
    """
    Serializer for project view count response
    """
    project_id = serializers.IntegerField()
    view_count = serializers.IntegerField()


class ProjectViewIncrementResponseSerializer(serializers.Serializer):
    """
    Serializer for project view increment response
    """
    project_id = serializers.IntegerField()
    old_view_count = serializers.IntegerField()
    new_view_count = serializers.IntegerField()
    updated = serializers.BooleanField()

    # Document download
    document_download_url = serializers.CharField(required=False, allow_null=True)


class MemberProjectSearchRequestSerializer(serializers.Serializer):
    """
    Serializer for member project search request parameters
    """
    # Search fields
    project_name = serializers.CharField(required=False, allow_blank=True, max_length=500)
    
    # Date range filtering
    announcement_start_date = serializers.DateField(required=False)
    announcement_end_date = serializers.DateField(required=False)
    
    # Filter options (เงื่อนไขกรองข้อมูล)
    filter_type = serializers.ChoiceField(
        choices=[
            ('latest', 'โครงการล่าสุด'),           # โครงการล่าสุด
            ('published', 'โครงการที่ประกาศ'),      # โครงการที่ประกาศ
            ('announcement_count', 'จำนวนที่ประกาศ')  # จำนวนที่ประกาศ
        ],
        required=False,
        default='latest',
        help_text="เลือกเงื่อนไขการกรองข้อมูล: latest=โครงการล่าสุด, published=โครงการที่ประกาศ, announcement_count=จำนวนที่ประกาศ"
    )
    
    def validate(self, data):
        """Cross-field validation"""
        # Validate date ranges
        if data.get('announcement_start_date') and data.get('announcement_end_date'):
            if data['announcement_start_date'] > data['announcement_end_date']:
                raise serializers.ValidationError({
                    'announcement_end_date': 'วันที่สิ้นสุดการประกาศต้องมากกว่าหรือเท่ากับวันที่เริ่มต้น'
                })
        return data


class MemberProjectSearchItemSerializer(serializers.Serializer):
    """
    Serializer for individual member project search item
    """
    project_id = serializers.IntegerField()
    project_name = serializers.CharField()
    announcement_date = serializers.CharField()
    status = serializers.IntegerField()
    status_name = serializers.CharField()
    matching_count = serializers.IntegerField()
    interested_count = serializers.IntegerField()
    view_count = serializers.IntegerField()


class MemberProjectSearchResponseSerializer(serializers.Serializer):
    """
    Serializer for member project search response
    """
    results = MemberProjectSearchItemSerializer(many=True)
    pagination = serializers.DictField()


class SuitableConsultantRequestSerializer(serializers.Serializer):
    """
    Serializer for suitable consultant request parameters
    """
    # Consultant type filter (1: Independent, 2: Corporate, None: All)
    consultant_type_filter_1 = serializers.IntegerField(required=False, allow_null=True)
    
    # Corporate type filter (only applicable when consultant_type_filter_1 = 2)
    consultant_type_filter_2 = serializers.CharField(required=False, allow_blank=True, allow_null=True)
    
    # Sorting options
    sort_by_matching = serializers.BooleanField(required=False, default=False)
    sort_by_latest = serializers.BooleanField(required=False, default=False)

    def validate_consultant_type_filter_1(self, value):
        """Validate consultant type filter 1"""
        if value is not None and value not in [1, 2]:
            raise serializers.ValidationError("Consultant type filter must be 1 (Independent) or 2 (Corporate)")
        return value

    def validate_consultant_type_filter_2(self, value):
        """Validate consultant type filter 2 (corporate_type_id)"""
        if value is not None and value.strip() != "":
            try:
                corporate_type_id = int(value.strip())
                if corporate_type_id <= 0:
                    raise serializers.ValidationError("Corporate type ID must be a positive integer")
                return str(corporate_type_id)
            except ValueError:
                raise serializers.ValidationError("Corporate type ID must be a valid integer")
        return value

    def validate(self, data):
        """Cross-field validation"""
        # If consultant_type_filter_2 is provided, consultant_type_filter_1 should be 2
        if data.get('consultant_type_filter_2') and data.get('consultant_type_filter_1') != 2:
            raise serializers.ValidationError({
                'consultant_type_filter_2': 'Corporate type filter can only be used when consultant type is Corporate (2)'
            })
        
        # Only one sorting option should be selected
        if data.get('sort_by_matching') and data.get('sort_by_latest'):
            raise serializers.ValidationError({
                'sort_by_latest': 'Cannot sort by both matching and latest at the same time'
            })
        
        return data


class ConsultantStatusSerializer(serializers.Serializer):
    """
    Serializer for consultant status information
    """
    verify_status = serializers.CharField()
    verify_status_display = serializers.CharField()
    consult_type = serializers.IntegerField()
    consult_type_display = serializers.CharField()
    is_active_matching = serializers.BooleanField()
    score = serializers.FloatField()
    phone = serializers.CharField()
    email = serializers.CharField()


class ConsultantDetailSerializer(serializers.Serializer):
    """
    Serializer for consultant detail information
    """
    id = serializers.IntegerField()
    consult_type = serializers.IntegerField()
    consult_type_display = serializers.CharField()
    corporate_type_id = serializers.IntegerField(allow_null=True)
    corporate_type_display = serializers.CharField()
    username = serializers.CharField()
    email = serializers.CharField()
    email_second = serializers.CharField()
    phone = serializers.CharField()
    phone_second = serializers.CharField()
    maker_name = serializers.CharField(allow_null=True)
    maker_phone = serializers.CharField(allow_null=True)
    maker_email = serializers.CharField(allow_null=True)
    verify = serializers.CharField()
    verify_display = serializers.CharField()
    is_notification = serializers.CharField()
    score = serializers.FloatField()
    lang = serializers.CharField()
    is_active_matching = serializers.BooleanField()
    user_type = serializers.CharField()


class SuitableConsultantItemSerializer(serializers.Serializer):
    """
    Serializer for individual suitable consultant item
    """
    consultant_id = serializers.IntegerField()
    consultant_name = serializers.CharField()
    consultant_status = ConsultantStatusSerializer()
    favorite_status = serializers.IntegerField()  # 1: liked, 0: not liked
    matching_result = serializers.FloatField()
    register_no = serializers.IntegerField()
    rating = serializers.IntegerField()
    consultant_detail = ConsultantDetailSerializer()


class SuitableConsultantResponseSerializer(serializers.Serializer):
    """
    Serializer for suitable consultant response
    """
    results = SuitableConsultantItemSerializer(many=True)
    project_info = serializers.DictField()
    page = serializers.IntegerField()
    per_page = serializers.IntegerField()
    total = serializers.IntegerField()
    has_next = serializers.BooleanField() 


class TcdAppProjectConsultSerializer(serializers.Serializer):
    """
    Serializer for TcdAppProjectConsult model
    """
    # Foreign key references
    app_project_id = serializers.IntegerField()
    app_member_id = serializers.IntegerField()
    user_consult_id = serializers.IntegerField()
    
    # Core fields
    register_no = serializers.DecimalField(max_digits=18, decimal_places=0)
    rating = serializers.DecimalField(max_digits=18, decimal_places=0)
    matching = serializers.FloatField()
    
    # Member-related fields
    member_status_contact = serializers.IntegerField(required=False, allow_null=True)
    member_apoitment_date = serializers.DateTimeField(required=False, allow_null=True)
    member_status_work = serializers.IntegerField(required=False, allow_null=True)
    member_detail = serializers.CharField(required=False, allow_blank=True, allow_null=True)
    member_favorite = serializers.CharField(max_length=1)
    member_view = serializers.IntegerField()
    
    # Consultant-related fields
    consult_status_contact = serializers.IntegerField(required=False, allow_null=True)
    consult_apoitment_date = serializers.DateTimeField(required=False, allow_null=True)
    consult_status_work = serializers.IntegerField(required=False, allow_null=True)
    consult_detail = serializers.CharField(required=False, allow_blank=True, allow_null=True)
    consult_favorite = serializers.CharField(max_length=1)
    consult_view = serializers.IntegerField()
    consult_send = serializers.IntegerField()
    consult_send_date = serializers.DateTimeField(required=False, allow_null=True)
    
    # Metadata
    update_date = serializers.DateTimeField(required=False, allow_null=True)

    def validate_member_favorite(self, value):
        """Validate member_favorite field"""
        if value not in ['Y', 'N', '1', '0']:
            raise serializers.ValidationError("Member favorite must be 'Y', 'N', '1', or '0'")
        return value

    def validate_consult_favorite(self, value):
        """Validate consult_favorite field"""
        if value not in ['Y', 'N', '1', '0']:
            raise serializers.ValidationError("Consult favorite must be 'Y', 'N', '1', or '0'")
        return value

    def validate_matching(self, value):
        """Validate matching score"""
        if value < 0 or value > 100:
            raise serializers.ValidationError("Matching score must be between 0 and 100")
        return value

    def validate_register_no(self, value):
        """Validate register number"""
        if value <= 0:
            raise serializers.ValidationError("Register number must be positive")
        return value

    def validate_rating(self, value):
        """Validate rating"""
        if value < 0:
            raise serializers.ValidationError("Rating must be non-negative")
        return value


class TcdAppProjectConsultCreateSerializer(serializers.Serializer):
    """
    Serializer for creating TcdAppProjectConsult entries
    """
    app_project_id = serializers.IntegerField()
    app_member_id = serializers.IntegerField()
    user_consult_id = serializers.IntegerField()
    register_no = serializers.DecimalField(max_digits=18, decimal_places=0)
    rating = serializers.DecimalField(max_digits=18, decimal_places=0)
    matching = serializers.FloatField()
    member_favorite = serializers.CharField(max_length=1, default='N')
    member_view = serializers.IntegerField(default=0)
    consult_favorite = serializers.CharField(max_length=1, default='N')
    consult_view = serializers.IntegerField(default=0)
    consult_send = serializers.IntegerField(default=0)
    
    # Optional fields
    member_status_contact = serializers.IntegerField(required=False, allow_null=True)
    member_apoitment_date = serializers.DateTimeField(required=False, allow_null=True)
    member_status_work = serializers.IntegerField(required=False, allow_null=True)
    member_detail = serializers.CharField(required=False, allow_blank=True, allow_null=True)
    consult_status_contact = serializers.IntegerField(required=False, allow_null=True)
    consult_apoitment_date = serializers.DateTimeField(required=False, allow_null=True)
    consult_status_work = serializers.IntegerField(required=False, allow_null=True)
    consult_detail = serializers.CharField(required=False, allow_blank=True, allow_null=True)
    consult_send_date = serializers.DateTimeField(required=False, allow_null=True)

    def validate_member_favorite(self, value):
        """Validate member_favorite field"""
        if value not in ['Y', 'N', '1', '0']:
            raise serializers.ValidationError("Member favorite must be 'Y', 'N', '1', or '0'")
        return value

    def validate_consult_favorite(self, value):
        """Validate consult_favorite field"""
        if value not in ['Y', 'N', '1', '0']:
            raise serializers.ValidationError("Consult favorite must be 'Y', 'N', '1', or '0'")
        return value

    def validate_matching(self, value):
        """Validate matching score"""
        if value < 0 or value > 100:
            raise serializers.ValidationError("Matching score must be between 0 and 100")
        return value

    def validate_register_no(self, value):
        """Validate register number"""
        if value <= 0:
            raise serializers.ValidationError("Register number must be positive")
        return value

    def validate_rating(self, value):
        """Validate rating"""
        if value < 0:
            raise serializers.ValidationError("Rating must be non-negative")
        return value


class TcdAppProjectConsultUpdateSerializer(serializers.Serializer):
    """
    Serializer for updating TcdAppProjectConsult entries
    """
    # Core fields that can be updated
    matching = serializers.FloatField(required=False)
    
    # Member-related fields
    member_status_contact = serializers.IntegerField(required=False, allow_null=True)
    member_apoitment_date = serializers.DateTimeField(required=False, allow_null=True)
    member_status_work = serializers.IntegerField(required=False, allow_null=True)
    member_detail = serializers.CharField(required=False, allow_blank=True, allow_null=True)
    member_favorite = serializers.CharField(max_length=1, required=False)
    member_view = serializers.IntegerField(required=False)
    
    # Consultant-related fields
    consult_status_contact = serializers.IntegerField(required=False, allow_null=True)
    consult_apoitment_date = serializers.DateTimeField(required=False, allow_null=True)
    consult_status_work = serializers.IntegerField(required=False, allow_null=True)
    consult_detail = serializers.CharField(required=False, allow_blank=True, allow_null=True)
    consult_favorite = serializers.CharField(max_length=1, required=False)
    consult_view = serializers.IntegerField(required=False)
    consult_send = serializers.IntegerField(required=False)
    consult_send_date = serializers.DateTimeField(required=False, allow_null=True)

    def validate_member_favorite(self, value):
        """Validate member_favorite field"""
        if value and value not in ['Y', 'N', '1', '0']:
            raise serializers.ValidationError("Member favorite must be 'Y', 'N', '1', or '0'")
        return value

    def validate_consult_favorite(self, value):
        """Validate consult_favorite field"""
        if value and value not in ['Y', 'N', '1', '0']:
            raise serializers.ValidationError("Consult favorite must be 'Y', 'N', '1', or '0'")
        return value

    def validate_matching(self, value):
        """Validate matching score"""
        if value is not None and (value < 0 or value > 100):
            raise serializers.ValidationError("Matching score must be between 0 and 100")
        return value


class TcdAppProjectConsultListSerializer(serializers.Serializer):
    """
    Serializer for listing TcdAppProjectConsult entries with related data
    """
    id = serializers.IntegerField(required=False)
    
    # Project information
    project_name = serializers.CharField()
    project_id = serializers.IntegerField()
    
    # Member information
    member_name = serializers.CharField()
    member_id = serializers.IntegerField()
    
    # Consultant information
    consultant_name = serializers.CharField()
    consultant_id = serializers.IntegerField()
    
    # Core matching data
    register_no = serializers.DecimalField(max_digits=18, decimal_places=0)
    rating = serializers.DecimalField(max_digits=18, decimal_places=0)
    matching = serializers.FloatField()
    
    # Status information
    member_status_contact = serializers.IntegerField(required=False, allow_null=True)
    member_status_work = serializers.IntegerField(required=False, allow_null=True)
    consult_status_contact = serializers.IntegerField(required=False, allow_null=True)
    consult_status_work = serializers.IntegerField(required=False, allow_null=True)
    
    # Favorite status
    member_favorite = serializers.CharField(max_length=1)
    consult_favorite = serializers.CharField(max_length=1)
    
    # View counts
    member_view = serializers.IntegerField()
    consult_view = serializers.IntegerField()
    consult_send = serializers.IntegerField()
    
    # Dates
    member_apoitment_date = serializers.DateTimeField(required=False, allow_null=True)
    consult_apoitment_date = serializers.DateTimeField(required=False, allow_null=True)
    consult_send_date = serializers.DateTimeField(required=False, allow_null=True)
    update_date = serializers.DateTimeField(required=False, allow_null=True)


class InterestedConsultantDetailsSerializer(serializers.Serializer):
    """
    Serializer for interested consultant details
    """
    consult_type = serializers.IntegerField()
    consult_type_display = serializers.CharField()
    corporate_type_id = serializers.IntegerField(allow_null=True)
    corporate_type_display = serializers.CharField(allow_null=True)
    username = serializers.CharField()
    email = serializers.CharField()
    phone = serializers.CharField()
    verify = serializers.CharField()
    verify_display = serializers.CharField()
    score = serializers.FloatField()
    is_active_matching = serializers.BooleanField()
    lang = serializers.CharField()


class InterestedConsultantItemSerializer(serializers.Serializer):
    """
    Serializer for individual interested consultant item
    """
    id = serializers.IntegerField()
    consultant_id = serializers.IntegerField()
    consultant_name = serializers.CharField()
    consultant_details = InterestedConsultantDetailsSerializer()
    matching_result = serializers.FloatField()
    register_no = serializers.IntegerField()
    rating = serializers.IntegerField()
    consult_send_date = serializers.CharField(allow_null=True)
    member_view = serializers.IntegerField()
    consult_view = serializers.IntegerField()
    member_favorite = serializers.BooleanField()
    consult_favorite = serializers.BooleanField()


class ProjectInterestedConsultantsRequestSerializer(serializers.Serializer):
    """
    Serializer for project interested consultants request
    """
    sort_by_matching = serializers.BooleanField(required=False, default=False, help_text="ถ้าเลือก True จะเรียงลำดับตามผลการจับคู่จาก มาก-น้อย (matching DESC, rating ASC, register_no ASC), ถ้า False จะเรียงตาม id DESC")


class ProjectInterestedConsultantsResponseSerializer(serializers.Serializer):
    """
    Serializer for project interested consultants response
    """
    results = InterestedConsultantItemSerializer(many=True)
    pagination = serializers.DictField()
    project_info = serializers.DictField()


class ProjectFavoriteConsultantsRequestSerializer(serializers.Serializer):
    """
    Serializer for project favorite consultants request
    """
    sort_by_matching = serializers.BooleanField(required=False, default=False, help_text="ถ้าเลือก True จะเรียงลำดับตามผลการจับคู่จาก มาก-น้อย (matching DESC, rating ASC, register_no ASC), ถ้า False จะเรียงตาม id DESC")


class FavoriteConsultantDetailsSerializer(serializers.Serializer):
    """
    Serializer for favorite consultant details
    """
    consult_type = serializers.IntegerField()
    consult_type_display = serializers.CharField()
    corporate_type_id = serializers.IntegerField(allow_null=True)
    corporate_type_display = serializers.CharField(allow_null=True)
    username = serializers.CharField()
    email = serializers.CharField()
    phone = serializers.CharField()
    verify = serializers.CharField()
    verify_display = serializers.CharField()
    score = serializers.FloatField()
    is_active_matching = serializers.BooleanField()
    lang = serializers.CharField()


class FavoriteConsultantItemSerializer(serializers.Serializer):
    """
    Serializer for individual favorite consultant item
    """
    id = serializers.IntegerField()
    consultant_id = serializers.IntegerField()
    consultant_name = serializers.CharField()
    consultant_details = FavoriteConsultantDetailsSerializer()
    matching_result = serializers.FloatField()
    register_no = serializers.IntegerField()
    rating = serializers.IntegerField()
    consult_send_date = serializers.CharField(allow_null=True)
    member_view = serializers.IntegerField()
    consult_view = serializers.IntegerField()
    member_favorite = serializers.BooleanField()
    consult_favorite = serializers.BooleanField()


class ProjectFavoriteConsultantsResponseSerializer(serializers.Serializer):
    """
    Serializer for project favorite consultants response
    """
    results = FavoriteConsultantItemSerializer(many=True)
    pagination = serializers.DictField()
    project_info = serializers.DictField()


class ConsultantExperienceRequestSerializer(serializers.Serializer):
    """
    Serializer for consultant experience request parameters
    """
    project_id = serializers.IntegerField(required=False, allow_null=True, help_text="ID ของโครงการ")
    general_data_id = serializers.IntegerField(required=True, allow_null=False, help_text="ID ของข้อมูลทั่วไป")
    user_consult_id = serializers.IntegerField(required=True, min_value=1, help_text="ID ของที่ปรึกษา")
    consult_type = serializers.IntegerField(required=True, min_value=1, help_text="ประเภทที่ปรึกษา (1: อิสระ, 2: นิติบุคคล)")
    corporate_type_id = serializers.IntegerField(required=False, allow_null=True, min_value=1, help_text="ID ของนิติบุคคล")
    
    def validate_consult_type(self, value):
        """Validate consult_type field"""
        if value not in [1, 2]:
            raise serializers.ValidationError("ประเภทที่ปรึกษาต้องเป็น 1 (อิสระ) หรือ 2 (นิติบุคคล)")
        return value


class ConsultantExperienceItemSerializer(serializers.Serializer):
    """
    Serializer for individual consultant experience item
    """
    project_id = serializers.IntegerField()
    project_name = serializers.CharField()
    start_date = serializers.CharField()
    stop_date = serializers.CharField()
    sectors = serializers.CharField(help_text="รายการสาขาที่เกี่ยวข้อง (คั่นด้วย ', ')")
    skills = serializers.CharField(help_text="รายการความเชี่ยวชาญที่เกี่ยวข้อง (คั่นด้วย ', ')")
    services = serializers.CharField(help_text="รายการบริการที่เกี่ยวข้อง (คั่นด้วย ', ')")


class ConsultantExperienceResponseSerializer(serializers.Serializer):
    """
    Serializer for consultant experience response
    """
    results = ConsultantExperienceItemSerializer(many=True)
    pagination = serializers.DictField(
        help_text="ข้อมูล pagination ประกอบด้วย page, page_size, total_count, has_next"
    )


class ConsultantMemberViewResponseSerializer(serializers.Serializer):
    """
    Serializer for consultant member view increment response
    """
    id = serializers.IntegerField()
    app_project_id = serializers.IntegerField()
    user_consult_id = serializers.IntegerField()
    app_member_id = serializers.IntegerField()
    old_member_view = serializers.IntegerField()
    new_member_view = serializers.IntegerField()
    matching = serializers.FloatField()
    updated = serializers.BooleanField()


class ConsultantMemberViewRequestSerializer(serializers.Serializer):
    """
    Serializer for consultant member view increment request
    """
    app_project_id = serializers.IntegerField(required=True, min_value=1, help_text="ID ของโครงการ")
    user_consult_id = serializers.IntegerField(required=True, min_value=1, help_text="ID ของที่ปรึกษาที่เลือก")


class ProjectMemberFavoriteRequestSerializer(serializers.Serializer):
    """
    Serializer for project member favorite request parameters
    """
    app_project_id = serializers.IntegerField(required=True, min_value=1, help_text="ID ของโครงการที่เลือก")
    user_consult_id = serializers.IntegerField(required=True, min_value=1, help_text="ID ของที่ปรึกษาที่เลือก")


class ProjectMemberFavoriteStatusResponseSerializer(serializers.Serializer):
    """
    Serializer for project member favorite status response
    """
    id = serializers.IntegerField()
    app_project_id = serializers.IntegerField()
    user_consult_id = serializers.IntegerField()
    app_member_id = serializers.IntegerField()
    member_favorite = serializers.CharField(max_length=1)
    is_favorite = serializers.BooleanField()


class ProjectMemberFavoriteUpdateResponseSerializer(serializers.Serializer):
    """
    Serializer for project member favorite update response
    """
    id = serializers.IntegerField()
    app_project_id = serializers.IntegerField()
    user_consult_id = serializers.IntegerField()
    app_member_id = serializers.IntegerField()
    old_member_favorite = serializers.CharField(max_length=1)
    new_member_favorite = serializers.CharField(max_length=1)
    is_favorite = serializers.BooleanField()
    action = serializers.CharField()
    updated = serializers.BooleanField()


class ConsultantSuitableProjectsRequestSerializer(serializers.Serializer):
    """
    Serializer for consultant suitable projects request parameters
    """
    sector_id = serializers.IntegerField(required=False, allow_null=True, min_value=1, help_text="ID ตัวกรอง ข้อมูลสาขา")
    sort_by_matching = serializers.BooleanField(required=False, default=False, help_text="ถ้าเลือก True จะเรียงลำดับตามผลการจับคู่จาก มาก-น้อย (matching DESC, start_date ASC), ถ้า False จะเรียงตาม id DESC")


class ConsultantSuitableProjectItemSerializer(serializers.Serializer):
    """
    Serializer for individual consultant suitable project item
    """
    project_id = serializers.IntegerField()
    project_name = serializers.CharField()
    organization_name = serializers.CharField()
    announcement_period = serializers.CharField()
    favorite_status = serializers.CharField(max_length=1, help_text="สถานะการถูกใจ: '1' = กดถูกใจ, '0' = ไม่ได้กดถูกใจ")
    matching_result = serializers.FloatField()
    view_count = serializers.IntegerField()


class ConsultantSuitableProjectsResponseSerializer(serializers.Serializer):
    """
    Serializer for consultant suitable projects response
    """
    results = ConsultantSuitableProjectItemSerializer(many=True)
    pagination = serializers.DictField()


class ConsultantFavoriteProjectsRequestSerializer(serializers.Serializer):
    """
    Serializer for consultant favorite projects request parameters
    """
    sort_by_matching = serializers.BooleanField(required=False, default=False, help_text="ถ้าเลือก True จะเรียงลำดับตามผลการจับคู่จาก มาก-น้อย (matching DESC, start_date ASC), ถ้า False จะเรียงตาม id DESC")


class ConsultantFavoriteProjectItemSerializer(serializers.Serializer):
    """
    Serializer for individual consultant favorite project item
    """
    project_id = serializers.IntegerField()
    project_name = serializers.CharField()
    organization_name = serializers.CharField()
    announcement_date = serializers.CharField()
    favorite_status = serializers.CharField(max_length=1)
    matching_result = serializers.FloatField()
    view_count = serializers.IntegerField()


class ConsultantFavoriteProjectsResponseSerializer(serializers.Serializer):
    """
    Serializer for consultant favorite projects response
    """
    results = ConsultantFavoriteProjectItemSerializer(many=True)
    pagination = serializers.DictField()


class ConsultantFavoriteStatusResponseSerializer(serializers.Serializer):
    """
    Serializer for consultant favorite status response
    """
    id = serializers.IntegerField()
    app_project_id = serializers.IntegerField()
    user_consult_id = serializers.IntegerField()
    consult_favorite = serializers.CharField(max_length=1)
    is_favorite = serializers.BooleanField()


class ConsultantFavoriteUpdateResponseSerializer(serializers.Serializer):
    """
    Serializer for consultant favorite update response
    """
    id = serializers.IntegerField()
    app_project_id = serializers.IntegerField()
    user_consult_id = serializers.IntegerField()
    old_consult_favorite = serializers.CharField(max_length=1)
    new_consult_favorite = serializers.CharField(max_length=1)
    is_favorite = serializers.BooleanField()
    action = serializers.CharField()
    updated = serializers.BooleanField()


class MasterDataSerializer(serializers.Serializer):
    """
    Serializer for master data fields (e.g., consultant experience, result, etc.)
    """
    id = serializers.IntegerField(allow_null=True)
    name_th = serializers.CharField(allow_null=True)
    name_en = serializers.CharField(allow_null=True)


class ProjectDetailExtendedRequestSerializer(serializers.Serializer):
    """
    Serializer for project detail extended request parameters
    """
    app_project_id = serializers.IntegerField(required=True, min_value=1, help_text="ID ของโครงการที่ต้องการดูรายละเอียด")


class OrganizationDetailExtendedSerializer(serializers.Serializer):
    """
    Serializer for organization information in extended project detail
    """
    name = serializers.CharField()
    phone = serializers.CharField(allow_null=True)
    email = serializers.CharField(allow_null=True)
    type = serializers.CharField()
    website = serializers.CharField(allow_null=True)


class MatchingResultSerializer(serializers.Serializer):
    """
    Serializer for matching result information
    """
    matching_score = serializers.FloatField(allow_null=True)
    consult_send = serializers.IntegerField(allow_null=True)


class ContactButtonExtendedSerializer(serializers.Serializer):
    """
    Serializer for contact button information
    """
    text = serializers.CharField()
    text_en = serializers.CharField()
    sent = serializers.BooleanField()


class SkillDetailExtendedSerializer(serializers.Serializer):
    """
    Serializer for skill information
    """
    code = serializers.CharField()
    name_th = serializers.CharField()
    name_en = serializers.CharField()
    display = serializers.CharField()


class SectorDetailExtendedSerializer(serializers.Serializer):
    """
    Serializer for sector information with skills
    """
    code = serializers.CharField()
    name_th = serializers.CharField()
    name_en = serializers.CharField()
    display = serializers.CharField()
    skills = SkillDetailExtendedSerializer(many=True)


class ServiceDetailExtendedSerializer(serializers.Serializer):
    """
    Serializer for service information
    """
    code = serializers.CharField()
    name_th = serializers.CharField()
    name_en = serializers.CharField()
    display = serializers.CharField()


class ProjectDetailExtendedResponseSerializer(serializers.Serializer):
    """
    Serializer for extended project detail response with all master data relationships
    """
    # Basic project information
    project_id = serializers.IntegerField()
    project_name = serializers.CharField()
    view_count = serializers.IntegerField()
    purpose = serializers.CharField(allow_null=True, help_text="วัตถุประสงค์โครงการ")
    activity = serializers.CharField(allow_null=True, help_text="ขอบเขตการดำเนินงาน")
    project_period = serializers.CharField(allow_null=True, help_text="ระยะเวลาโครงการ")
    announcement_period = serializers.CharField(allow_null=True, help_text="วันที่ประกาศโครงการ")
    keyword = serializers.CharField(allow_null=True, help_text="คำค้นหา (Keyword)")
    document_url = serializers.CharField(allow_null=True, help_text="ปุ่ม ดาวน์โหลดเอกสาร")
    
    # Organization data (ข้อมูลหน่วยงาน)
    organization = OrganizationDetailExtendedSerializer(allow_null=True)
    
    # Consultant matching data (only for consultants)
    matching_result = MatchingResultSerializer(allow_null=True, help_text="ผลจับคู่ - แสดงเมื่อเป็นที่ปรึกษาและมีการ matching")
    contact_button = ContactButtonExtendedSerializer(allow_null=True, help_text="ปุ่ม ส่งข้อมูลติดต่อ - แสดงเมื่อเป็นที่ปรึกษาและมีการ matching")
    
    # Sectors and skills data (ข้อมูลสาขา ความเชี่ยวชาญ)
    sectors_skills = SectorDetailExtendedSerializer(many=True, help_text="ข้อมูลสาขา ความเชี่ยวชาญ")
    
    # Services data (การบริการ)
    services = ServiceDetailExtendedSerializer(many=True, help_text="การบริการ")
    
    # Master data with language support
    project_sector_count = serializers.CharField(allow_null=True, help_text="จำนวนโครงการที่สอดคล้องกับสาขา")
    consultant_experience = serializers.CharField(allow_null=True, help_text="ประสบการณ์ที่ปรึกษา")
    project_cost = serializers.CharField(allow_null=True, help_text="มูลค่าสัญญาจ้างที่ปรึกษา")
    project_number = serializers.CharField(allow_null=True, help_text="จำนวนโครงการ")
    project_type = serializers.CharField(allow_null=True, help_text="ประเภทโครงการ")
    consultant_number = serializers.CharField(allow_null=True, help_text="จำนวนบุคลากรที่ปรึกษา")
    consultant_type = serializers.CharField(allow_null=True, help_text="ประเภทที่ปรึกษา")
    consultant_rating = serializers.CharField(allow_null=True, help_text="ระดับที่ปรึกษา")
    consultant_certificate = serializers.CharField(allow_null=True, help_text="หนังสือรับรองการขึ้นทะเบียน")
    matching_result_master = serializers.CharField(allow_null=True, help_text="ผลการจับคู่")


class ProjectInterestStatusRequestSerializer(serializers.Serializer):
    """
    Serializer for project interest status request parameters
    """
    app_project_id = serializers.IntegerField(required=True, min_value=1, help_text="ID ของโครงการที่เลือก")


class ProjectInterestStatusResponseSerializer(serializers.Serializer):
    """
    Serializer for project interest status response
    """
    id = serializers.IntegerField()
    app_project_id = serializers.IntegerField()
    user_consult_id = serializers.IntegerField()
    app_member_id = serializers.IntegerField()
    consult_send = serializers.IntegerField()
    is_interested = serializers.BooleanField()


class ProjectInterestUpdateRequestSerializer(serializers.Serializer):
    """
    Serializer for project interest update request parameters
    """
    app_project_id = serializers.IntegerField(required=True, min_value=1, help_text="ID ของโครงการที่เลือก")


class ProjectInterestUpdateResponseSerializer(serializers.Serializer):
    """
    Serializer for project interest update response
    """
    id = serializers.IntegerField()
    app_project_id = serializers.IntegerField()
    user_consult_id = serializers.IntegerField()
    app_member_id = serializers.IntegerField()
    old_consult_send = serializers.IntegerField()
    new_consult_send = serializers.IntegerField()
    is_interested = serializers.BooleanField()
    action = serializers.CharField()
    updated = serializers.BooleanField()

