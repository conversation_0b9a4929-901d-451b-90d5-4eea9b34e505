from django.db import models
from authentication.models import TcdAppMember, TcdUserConsult


class TcdAppMasConsultExp(models.Model):
    id = models.IntegerField(primary_key=True)
    name_th = models.CharField(max_length=250, db_collation='Thai_CI_AI')
    name_en = models.CharField(max_length=250, db_collation='Thai_CI_AI')

    class Meta:
        managed = False
        db_table = 'tcd_app_mas_consult_exp'


class TcdAppMasResult(models.Model):
    id = models.IntegerField(primary_key=True)
    name_th = models.CharField(max_length=250, db_collation='Thai_CI_AI')
    name_en = models.CharField(max_length=250, db_collation='Thai_CI_AI')

    class Meta:
        managed = False
        db_table = 'tcd_app_mas_result'


class TcdAppMasProjectCost(models.Model):
    id = models.IntegerField(primary_key=True)
    name_th = models.CharField(max_length=250, db_collation='Thai_CI_AI')
    name_en = models.CharField(max_length=250, db_collation='Thai_CI_AI')

    class Meta:
        managed = False
        db_table = 'tcd_app_mas_project_cost'


class TcdAppMasProjectNumber(models.Model):
    id = models.IntegerField(primary_key=True)
    name_th = models.CharField(max_length=250, db_collation='Thai_CI_AI')
    name_en = models.CharField(max_length=250, db_collation='Thai_CI_AI')

    class Meta:
        managed = False
        db_table = 'tcd_app_mas_project_number'


class TcdAppMasProjectType(models.Model):
    id = models.IntegerField(primary_key=True)
    name_th = models.CharField(max_length=250, db_collation='Thai_CI_AI')
    name_en = models.CharField(max_length=250, db_collation='Thai_CI_AI')

    class Meta:
        managed = False
        db_table = 'tcd_app_mas_project_type'


class TcdAppMasConsultNumber(models.Model):
    id = models.IntegerField(primary_key=True)
    name_th = models.CharField(max_length=250, db_collation='Thai_CI_AI')
    name_en = models.CharField(max_length=250, db_collation='Thai_CI_AI')

    class Meta:
        managed = False
        db_table = 'tcd_app_mas_consult_number'


class TcdAppMasConsultType(models.Model):
    id = models.IntegerField(primary_key=True)
    name_th = models.CharField(max_length=250, db_collation='Thai_CI_AI')
    name_en = models.CharField(max_length=250, db_collation='Thai_CI_AI')

    class Meta:
        managed = False
        db_table = 'tcd_app_mas_consult_type'


class TcdAppMasConsultRating(models.Model):
    id = models.IntegerField(primary_key=True)
    name_th = models.CharField(max_length=250, db_collation='Thai_CI_AI')
    name_en = models.CharField(max_length=250, db_collation='Thai_CI_AI')

    class Meta:
        managed = False
        db_table = 'tcd_app_mas_consult_rating'


class TcdAppMasConsultCertificate(models.Model):
    id = models.IntegerField(primary_key=True)
    name_th = models.CharField(max_length=250, db_collation='Thai_CI_AI')
    name_en = models.CharField(max_length=250, db_collation='Thai_CI_AI')

    class Meta:
        managed = False
        db_table = 'tcd_app_mas_consult_certificate'


class TcdAppProject(models.Model):
    id = models.IntegerField(primary_key=True)
    app_member = models.ForeignKey(TcdAppMember, on_delete=models.CASCADE)
    name = models.CharField(max_length=500, db_collation='Thai_CI_AI')
    purpost = models.CharField(max_length=8000, db_collation='Thai_CI_AI')
    activity = models.CharField(max_length=8000, db_collation='Thai_CI_AI')
    ref = models.CharField(max_length=255, db_collation='Thai_CI_AI')
    period_start = models.DateTimeField()
    period_end = models.DateTimeField()
    start_date = models.DateTimeField()
    end_date = models.DateTimeField()
    keyword = models.CharField(max_length=50, db_collation='Thai_CI_AI', blank=True, null=True)
    project_sector = models.IntegerField(blank=True, null=True)
    consult_exp = models.IntegerField(blank=True, null=True)
    project_cost = models.IntegerField(blank=True, null=True)
    project_number = models.IntegerField(blank=True, null=True)
    project_type = models.IntegerField(blank=True, null=True)
    consult_number = models.IntegerField(blank=True, null=True)
    consult_type = models.IntegerField(blank=True, null=True)
    consult_rating = models.IntegerField(blank=True, null=True)
    consult_certificate = models.IntegerField(blank=True, null=True)
    result = models.IntegerField(blank=True, null=True)
    url = models.CharField(max_length=50, db_collation='Thai_CI_AI', blank=True, null=True)
    view = models.IntegerField()
    create_date = models.DateTimeField()
    update_date = models.DateTimeField(blank=True, null=True)
    status = models.CharField(max_length=1, db_collation='Thai_CI_AI')
    is_complete = models.BooleanField()

    class Meta:
        managed = False
        db_table = 'tcd_app_project'


class TcdAppProjectSector(models.Model):
    app_project_id = models.IntegerField()
    sector_id = models.IntegerField()

    class Meta:
        managed = False
        db_table = 'tcd_app_project_sector'


class TcdAppProjectConsult(models.Model):
    app_project_id = models.IntegerField()
    app_member_id = models.IntegerField()
    user_consult_id = models.IntegerField()
    register_no = models.IntegerField()
    rating = models.IntegerField()
    matching = models.FloatField()
    member_status_contact = models.IntegerField(blank=True, null=True)
    member_apoitment_date = models.DateTimeField(blank=True, null=True)
    member_status_work = models.IntegerField(blank=True, null=True)
    member_detail = models.TextField(db_collation='Thai_CI_AI', blank=True, null=True)
    member_favorite = models.CharField(max_length=1, db_collation='Thai_CI_AI')
    member_view = models.IntegerField()
    consult_status_contact = models.IntegerField(blank=True, null=True)
    consult_apoitment_date = models.DateTimeField(blank=True, null=True)
    consult_status_work = models.IntegerField(blank=True, null=True)
    consult_detail = models.TextField(db_collation='Thai_CI_AI', blank=True, null=True)
    consult_favorite = models.CharField(max_length=1, db_collation='Thai_CI_AI')
    consult_view = models.IntegerField()
    consult_send = models.IntegerField()
    consult_send_date = models.DateTimeField(blank=True, null=True)
    update_date = models.DateTimeField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tcd_app_project_consult'


class TcdAppProjectSkill(models.Model):
    app_project_id = models.IntegerField()
    skill_id = models.IntegerField()
    app_project_sector_id = models.IntegerField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tcd_app_project_skill'


class TcdAppProjectService(models.Model):
    app_project_id = models.IntegerField()
    service_id = models.IntegerField()

    class Meta:
        managed = False
        db_table = 'tcd_app_project_service'


class TcdAppMatchingLog(models.Model):
    app_project_id = models.DecimalField(max_digits=18, decimal_places=0)
    user_consult_id = models.DecimalField(max_digits=18, decimal_places=0)
    matching = models.FloatField()
    project_name = models.CharField(max_length=250, db_collation='Thai_CI_AI')
    member_name = models.CharField(max_length=500, db_collation='Thai_CI_AI')
    consult_name = models.CharField(max_length=250, db_collation='Thai_CI_AI')
    create_date = models.DateTimeField()

    class Meta:
        managed = False
        db_table = 'tcd_app_matching_log'

